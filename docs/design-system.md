# MixCommunity 设计系统文档
## 视觉设计规范与组件库

### 版本信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-13
- **设计标准**: iOS Human Interface Guidelines
- **技术栈**: HTML5 + Tailwind CSS + FontAwesome
- **目标设备**: iPhone 15 Pro (393 × 852 pt)

---

## 1. 设计原则

### 1.1 核心设计理念
- **简洁性 (Simplicity)**: 去除不必要的视觉元素，专注于内容本身
- **一致性 (Consistency)**: 统一的视觉语言和交互模式
- **层次性 (Hierarchy)**: 清晰的信息层级和视觉重点
- **可访问性 (Accessibility)**: 确保所有用户都能良好使用
- **现代感 (Modernity)**: 符合当前设计趋势的现代化界面

### 1.2 iOS设计规范遵循
- **Human Interface Guidelines**: 严格遵循苹果官方设计指南
- **系统一致性**: 与iOS系统界面保持一致的视觉风格
- **触控优化**: 针对触屏设备优化的交互设计
- **动效自然**: 符合物理直觉的动画效果

---

## 2. 色彩系统 (Color System)

### 2.1 主色调 (Primary Colors)

#### 品牌主色 - 活力蓝 (Vibrant Blue)
- **主色**: `#007AFF` (iOS系统蓝)
- **浅色变体**: `#5AC8FA` (Light Blue)
- **深色变体**: `#0051D5` (Dark Blue)
- **使用场景**: 主要按钮、链接、选中状态、品牌标识

#### 强调色 - 创作橙 (Creative Orange)
- **主色**: `#FF9500` (iOS系统橙)
- **浅色变体**: `#FFCC02` (Light Orange)
- **深色变体**: `#D85F00` (Dark Orange)
- **使用场景**: 发布按钮、创作相关功能、重要提示

### 2.2 辅助色彩 (Secondary Colors)

#### 成功绿 (Success Green)
- **主色**: `#34C759` (iOS系统绿)
- **使用场景**: 成功状态、完成操作、正面反馈

#### 警告黄 (Warning Yellow)
- **主色**: `#FFCC00` (iOS系统黄)
- **使用场景**: 警告信息、注意事项

#### 错误红 (Error Red)
- **主色**: `#FF3B30` (iOS系统红)
- **使用场景**: 错误状态、删除操作、危险警告

#### 信息紫 (Info Purple)
- **主色**: `#AF52DE` (iOS系统紫)
- **使用场景**: 信息提示、特殊标记

### 2.3 中性色彩 (Neutral Colors)

#### 文字色彩层级
- **主要文字**: `#000000` (纯黑) - 标题、重要信息
- **次要文字**: `#3C3C43` (深灰) - 正文内容
- **辅助文字**: `#8E8E93` (中灰) - 说明文字、时间
- **禁用文字**: `#C7C7CC` (浅灰) - 禁用状态文字

#### 背景色彩层级
- **主背景**: `#FFFFFF` (纯白) - 主要背景
- **次背景**: `#F2F2F7` (浅灰) - 卡片背景、分组背景
- **分割线**: `#E5E5EA` (极浅灰) - 分割线、边框
- **遮罩**: `rgba(0, 0, 0, 0.4)` - 半透明遮罩

### 2.4 暗色模式适配 (Dark Mode)
- **主背景**: `#000000` (纯黑)
- **次背景**: `#1C1C1E` (深灰)
- **卡片背景**: `#2C2C2E` (中深灰)
- **主要文字**: `#FFFFFF` (纯白)
- **次要文字**: `#EBEBF5` (浅灰)

---

## 3. 字体系统 (Typography)

### 3.1 字体选择
- **iOS系统字体**: San Francisco (SF Pro)
- **Web字体**: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif
- **中文字体**: PingFang SC, Hiragino Sans GB, Microsoft YaHei

### 3.2 字体层级 (Type Scale)

#### 标题层级
- **H1 - 大标题**: 34pt / 2.125rem, Bold (700)
- **H2 - 中标题**: 28pt / 1.75rem, Bold (700)
- **H3 - 小标题**: 22pt / 1.375rem, Semibold (600)
- **H4 - 子标题**: 20pt / 1.25rem, Semibold (600)

#### 正文层级
- **Body Large**: 17pt / 1.0625rem, Regular (400) - 主要正文
- **Body**: 16pt / 1rem, Regular (400) - 标准正文
- **Body Small**: 15pt / 0.9375rem, Regular (400) - 次要正文

#### 辅助文字
- **Caption**: 13pt / 0.8125rem, Regular (400) - 说明文字
- **Caption Small**: 11pt / 0.6875rem, Regular (400) - 极小说明文字

#### 按钮文字
- **Button Large**: 17pt / 1.0625rem, Semibold (600)
- **Button**: 16pt / 1rem, Semibold (600)
- **Button Small**: 15pt / 0.9375rem, Medium (500)

### 3.3 行高规范 (Line Height)
- **标题行高**: 字号 × 1.2 (紧凑)
- **正文行高**: 字号 × 1.5 (舒适阅读)
- **按钮行高**: 字号 × 1.0 (紧凑)

---

## 4. 间距系统 (Spacing System)

### 4.1 基础网格 (Base Grid)
- **基础单位**: 8pt / 0.5rem
- **网格系统**: 8的倍数 (8, 16, 24, 32, 40, 48, 56, 64...)

### 4.2 间距规范

#### 组件内间距 (Component Spacing)
- **XS**: 4pt / 0.25rem - 图标与文字间距
- **S**: 8pt / 0.5rem - 元素间小间距
- **M**: 16pt / 1rem - 元素间标准间距
- **L**: 24pt / 1.5rem - 组件间间距
- **XL**: 32pt / 2rem - 区块间间距

#### 页面布局间距 (Layout Spacing)
- **页面边距**: 16pt / 1rem (左右边距)
- **安全区域**: 44pt / 2.75rem (顶部状态栏)
- **Tab Bar高度**: 83pt / 5.1875rem (包含安全区域)
- **导航栏高度**: 44pt / 2.75rem (不含状态栏)

#### 内容间距 (Content Spacing)
- **段落间距**: 16pt / 1rem
- **列表项间距**: 12pt / 0.75rem
- **卡片间距**: 16pt / 1rem
- **分组间距**: 32pt / 2rem

---

## 5. 圆角系统 (Border Radius)

### 5.1 圆角规范
- **XS**: 4pt / 0.25rem - 小元素圆角 (标签、徽章)
- **S**: 8pt / 0.5rem - 按钮圆角
- **M**: 12pt / 0.75rem - 卡片圆角
- **L**: 16pt / 1rem - 大卡片圆角
- **XL**: 20pt / 1.25rem - 模态框圆角
- **圆形**: 50% - 头像、圆形按钮

### 5.2 特殊圆角
- **iPhone屏幕圆角**: 39pt / 2.4375rem (模拟真实设备)
- **Tab Bar圆角**: 0pt (iOS标准无圆角)
- **输入框圆角**: 10pt / 0.625rem (iOS标准)

---

## 6. 阴影系统 (Shadow System)

### 6.1 阴影层级

#### 卡片阴影 (Card Shadows)
- **轻微阴影**: `0 1px 3px rgba(0, 0, 0, 0.1)`
- **标准阴影**: `0 4px 6px rgba(0, 0, 0, 0.1)`
- **明显阴影**: `0 10px 15px rgba(0, 0, 0, 0.1)`
- **强烈阴影**: `0 20px 25px rgba(0, 0, 0, 0.15)`

#### 浮动阴影 (Floating Shadows)
- **按钮悬停**: `0 4px 8px rgba(0, 122, 255, 0.3)`
- **模态框**: `0 25px 50px rgba(0, 0, 0, 0.25)`
- **下拉菜单**: `0 10px 20px rgba(0, 0, 0, 0.15)`

### 6.2 iOS风格阴影
- **毛玻璃效果**: `backdrop-filter: blur(20px)`
- **分割线阴影**: `0 0.5px 0 rgba(0, 0, 0, 0.1)`

---

## 7. 组件库 (Component Library)

### 7.1 按钮组件 (Buttons)

#### 主要按钮 (Primary Button)
- **背景色**: 品牌主色 `#007AFF`
- **文字色**: 白色 `#FFFFFF`
- **圆角**: 8pt / 0.5rem
- **高度**: 44pt / 2.75rem (iOS标准触控高度)
- **内边距**: 16pt / 1rem (左右)
- **字体**: Button / 16pt / Semibold

#### 次要按钮 (Secondary Button)
- **背景色**: 透明
- **边框**: 1px solid `#007AFF`
- **文字色**: 品牌主色 `#007AFF`
- **其他规范**: 同主要按钮

#### 文字按钮 (Text Button)
- **背景色**: 透明
- **文字色**: 品牌主色 `#007AFF`
- **无边框**: 无
- **内边距**: 8pt / 0.5rem (左右)

#### 危险按钮 (Destructive Button)
- **背景色**: 错误红 `#FF3B30`
- **文字色**: 白色 `#FFFFFF`
- **其他规范**: 同主要按钮

### 7.2 输入框组件 (Input Fields)

#### 标准输入框
- **背景色**: 次背景 `#F2F2F7`
- **边框**: 无 (iOS风格)
- **圆角**: 10pt / 0.625rem
- **高度**: 44pt / 2.75rem
- **内边距**: 16pt / 1rem (左右), 12pt / 0.75rem (上下)
- **字体**: Body / 16pt / Regular
- **占位符色**: 辅助文字 `#8E8E93`

#### 搜索框
- **背景色**: 次背景 `#F2F2F7`
- **圆角**: 10pt / 0.625rem
- **左侧图标**: 搜索图标 `#8E8E93`
- **其他规范**: 同标准输入框

### 7.3 卡片组件 (Cards)

#### 内容卡片
- **背景色**: 主背景 `#FFFFFF`
- **圆角**: 12pt / 0.75rem
- **阴影**: 标准阴影
- **内边距**: 16pt / 1rem
- **边框**: 无

#### 用户卡片
- **头像尺寸**: 40pt / 2.5rem (圆形)
- **用户名字体**: Body / 16pt / Semibold
- **时间字体**: Caption / 13pt / Regular
- **时间颜色**: 辅助文字 `#8E8E93`

### 7.4 导航组件 (Navigation)

#### Tab Bar
- **背景色**: 主背景 `#FFFFFF` (毛玻璃效果)
- **高度**: 83pt / 5.1875rem (包含安全区域)
- **图标尺寸**: 24pt / 1.5rem
- **文字字体**: Caption / 11pt / Regular
- **选中色**: 品牌主色 `#007AFF`
- **未选中色**: 辅助文字 `#8E8E93`

#### 导航栏
- **背景色**: 主背景 `#FFFFFF` (毛玻璃效果)
- **高度**: 44pt / 2.75rem (不含状态栏)
- **标题字体**: H4 / 20pt / Semibold
- **按钮字体**: Body / 16pt / Regular

### 7.5 状态组件 (Status)

#### 加载状态
- **骨架屏**: 次背景 `#F2F2F7` + 动画
- **加载指示器**: iOS系统样式
- **加载文字**: Caption / 13pt / Regular

#### 空状态
- **图标尺寸**: 64pt / 4rem
- **图标颜色**: 辅助文字 `#8E8E93`
- **标题字体**: H3 / 22pt / Semibold
- **描述字体**: Body / 16pt / Regular

#### 错误状态
- **图标颜色**: 错误红 `#FF3B30`
- **重试按钮**: 次要按钮样式

---

## 8. 图标系统 (Icon System)

### 8.1 图标规范
- **图标库**: FontAwesome 6.0+ (免费版)
- **尺寸规范**: 16pt, 20pt, 24pt, 32pt
- **线条粗细**: 1.5px (Medium)
- **风格**: 线性图标为主，填充图标为辅

### 8.2 常用图标映射

#### Tab Bar图标
- **首页**: `fas fa-home` / `far fa-home`
- **发现**: `fas fa-search` / `far fa-search`
- **发布**: `fas fa-plus` (圆形背景)
- **消息**: `fas fa-comment` / `far fa-comment`
- **我的**: `fas fa-user` / `far fa-user`

#### 功能图标
- **点赞**: `fas fa-heart` / `far fa-heart`
- **评论**: `fas fa-comment` / `far fa-comment`
- **分享**: `fas fa-share`
- **收藏**: `fas fa-bookmark` / `far fa-bookmark`
- **更多**: `fas fa-ellipsis-h`
- **返回**: `fas fa-chevron-left`
- **关闭**: `fas fa-times`

---

## 9. 动效系统 (Animation System)

### 9.1 动画原则
- **自然性**: 符合物理直觉的动画曲线
- **一致性**: 统一的动画时长和缓动函数
- **性能**: 优先使用CSS transform和opacity
- **可控性**: 支持用户关闭动画的系统设置

### 9.2 动画规范

#### 基础动画
- **快速动画**: 200ms - 按钮点击、状态切换
- **标准动画**: 300ms - 页面切换、模态框
- **慢速动画**: 500ms - 复杂转场、加载动画

#### 缓动函数
- **标准缓动**: `cubic-bezier(0.4, 0.0, 0.2, 1)` - 大部分动画
- **减速缓动**: `cubic-bezier(0.0, 0.0, 0.2, 1)` - 进入动画
- **加速缓动**: `cubic-bezier(0.4, 0.0, 1, 1)` - 退出动画
- **弹性缓动**: `cubic-bezier(0.175, 0.885, 0.32, 1.275)` - 特殊效果

#### 页面转场
- **推入**: 从右侧滑入 (Push)
- **弹出**: 从底部弹出 (Modal)
- **淡入**: 透明度变化 (Fade)
- **缩放**: 从中心缩放 (Scale)

---

## 10. 响应式设计 (Responsive Design)

### 10.1 断点系统
- **iPhone SE**: 375px (最小支持)
- **iPhone 15**: 393px (主要目标)
- **iPhone 15 Pro Max**: 430px (大屏优化)

### 10.2 适配策略
- **流式布局**: 使用百分比和flex布局
- **弹性字体**: 使用rem单位，支持系统字体大小
- **触控优化**: 最小触控区域44pt × 44pt
- **安全区域**: 自动适配不同设备的安全区域

---

## 11. 可访问性规范 (Accessibility)

### 11.1 颜色对比度
- **正常文字**: 对比度 ≥ 4.5:1
- **大字体**: 对比度 ≥ 3:1
- **图标**: 对比度 ≥ 3:1

### 11.2 字体大小
- **最小字体**: 11pt (Caption Small)
- **推荐最小**: 13pt (Caption)
- **支持动态字体**: 跟随系统字体大小设置

### 11.3 交互区域
- **最小触控**: 44pt × 44pt
- **推荐触控**: 48pt × 48pt
- **间距**: 触控区域间至少8pt间距

---

*本设计系统将根据开发进展和用户反馈持续更新优化*
