package com.sh.game;

import com.sh.concurrent.AbstractCommand;
import com.sh.game.common.communication.notice.AppendAuctionNotice;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.sys.OtherData;
import com.sh.game.common.env.AppContext;
import com.sh.game.common.intf.IGameContext;
import com.sh.game.data.SysDataProvider;
import com.sh.game.system.activity.ActivityManager;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.user.UserLoginAuthManager;

import java.util.List;

public class LogicContext implements IGameContext {
    private static final LogicContext INSTANCE = new LogicContext();

    private LogicContext() {

    }

    public static LogicContext getInstance() {
        return INSTANCE;
    }

    @Override
    public long getOpenTime() {
        return GameContext.getOpenDayZeroTime();
    }

    @Override
    public int getOpenDays() {
        return GameContext.getOpenServerDay();
    }

    @Override
    public int getMergeDays() {
        return GameContext.getCombineServerDay();
    }

    @Override
    public boolean isActivityOpen(int activityType) {
        return ActivityManager.getInstance().getAvailable(activityType) != null;
    }

    @Override
    public void sendMail(long roleId, int mailId, List<Item> attachments, Object... params) {
        MailManager.getInstance().sendMail(roleId, mailId, attachments, params);
    }

    @Override
    public void appendAuction(int hostId, long unionId, List<Item> appends, long ... params) {
        if (appends == null || appends.isEmpty()) {
            return;
        }
        if (AppContext.getProcessor() != ProcessorId.SERVER_COMMON) {
            AppendAuctionNotice notice = new AppendAuctionNotice();
            notice.setUnionId(unionId);
            notice.setAppends(appends);
            GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.SERVER_COMMON, notice, 0L);
            return;
        }

    }

    @Override
    public boolean isDebug() {
        return GameContext.getOption().isDebug();
    }

    @Override
    public void asyncAuth(AbstractCommand command) {
        UserLoginAuthManager.getInstance().asyncAuth(command);
    }

    @Override
    public int getMergeCount() {
        return SysDataProvider.get(OtherData.class).getMergeCount();
    }

    @Override
    public int getOs() {
        return GameContext.getOption().getOs();
    }

}
