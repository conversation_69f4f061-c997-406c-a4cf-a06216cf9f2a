package com.sh.game.condition;

import com.sh.game.common.constant.ConditionTypeConst;
import com.sh.game.common.entity.usr.Role;

public class RoleConditionValidator {
    private static final RoleConditionValidator INSTANCE = new RoleConditionValidator();

    private RoleConditionValidator() {

    }

    public static RoleConditionValidator getInstance() {
        return INSTANCE;
    }


    public boolean validate(Role role, int[] params) {
        switch (params[0]) {
            case ConditionTypeConst.VIOLENT_POWER:
                boolean violentPower = role.getRoleAdvance().getBuffs().isViolentPower();
                return params[1] > 0 ? violentPower : !violentPower;
            default:
                return false;
        }
    }

}
