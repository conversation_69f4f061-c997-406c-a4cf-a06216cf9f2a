package com.sh.config;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.IConfigBaseTest;
import org.junit.jupiter.api.*;

/**
 * 上传流程检测 测试用例
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by Silence on 2021/9/17.
 */
@DisplayName("公共模块配置表测试")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class CommonConfigInitTest implements IConfigBaseTest {


    @Order(1)
    @BeforeAll
    @Override
    public void initConfig() {
        try {
            ConfigDataManager.getInstance().init(findConfigDataCustomization());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 无子类测试用例时也需要触发父类
     */
    @Test
    public void test() {

    }

}
