package com.sh.config.groovy;

import com.sh.game.server.ConnectionConst;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.http.*;

import java.io.File;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

public class NettyHttpClient {
    public static void main(String[] args) throws Exception {
        // 此处可更改
        int serverType = ConnectionConst.TYPE.SCENE;

        // 读取Groovy文件中的内容
        String groovyFilePath = serverType == ConnectionConst.TYPE.SERVER ?
                "game-server/src/test/java/com/sh/groovy/server.groovy" :
                "game-scene/src/test/java/com/sh/groovy/scene.groovy";

        String text = new String(Files.readAllBytes(new File(groovyFilePath).toPath()), StandardCharsets.UTF_8);

        // 请求的URI
        URI uri = new URI("http://localhost:9002/groovyScript?sign=2e7d2c03a9507ae265ecf5b5356885a3&serverType=" + serverType);
        String host = uri.getHost();
        int port = uri.getPort();

        EventLoopGroup group = new NioEventLoopGroup();
        try {
            Bootstrap b = new Bootstrap();
            b.group(group)
                    .channel(NioSocketChannel.class)
                    .option(ChannelOption.SO_KEEPALIVE, true)
                    .handler(new ChannelInitializer<NioSocketChannel>() {
                        @Override
                        public void initChannel(NioSocketChannel ch) throws Exception {
                            ch.pipeline().addLast(new HttpClientCodec());
                            ch.pipeline().addLast(new HttpObjectAggregator(8192));
                            ch.pipeline().addLast(new HttpContentDecompressor());
                            ch.pipeline().addLast(new HttpClientHandler(group));
                        }
                    });

            // 创建HTTP请求
            FullHttpRequest request = new DefaultFullHttpRequest(
                    HttpVersion.HTTP_1_1, HttpMethod.POST, uri.toASCIIString(),
                    Unpooled.copiedBuffer(text, StandardCharsets.UTF_8));

            request.headers().set(HttpHeaderNames.HOST, host);
            request.headers().set(HttpHeaderNames.CONTENT_TYPE, "text/plain");
            request.headers().set(HttpHeaderNames.CONTENT_LENGTH, request.content().readableBytes());

            // 发起连接并发送请求
            ChannelFuture f = b.connect(host, port).sync();
            f.channel().writeAndFlush(request);

            // 等待响应
            f.channel().closeFuture().sync();
        } finally {
            group.shutdownGracefully();
        }
    }
}

class HttpClientHandler extends SimpleChannelInboundHandler<FullHttpResponse> {

    private final EventLoopGroup group;

    public HttpClientHandler(EventLoopGroup group) {
        this.group = group;
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, FullHttpResponse response) throws Exception {
        if (response.status().equals(HttpResponseStatus.OK)) {
            System.out.println("Response received: " + response.content().toString(StandardCharsets.UTF_8));
        } else {
            System.err.println("Request failed with status: " + response.status());
        }

        // 在接收到响应后关闭通道
        ctx.close();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        // 在通道关闭后，关闭事件循环组以退出应用程序
        group.shutdownGracefully();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        cause.printStackTrace();
        ctx.close();
    }
}
