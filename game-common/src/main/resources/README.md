1. BagManager中不要随便加新方法,要先征得同意
2. protoBuf对数组支持有问题, 实体类中不要用int[]
3. @Tag一定不能重复,不然会出重大错误
4. 所有pojo类必须遵行:如果有 有参数构造函数, 则得手动加一个无参的构造函数。不然会有空指针慢常
5. int[] byte[] long[] short[] double[] float[] ....  基本类型可以用数组  初始化为0 String 不支持
6. List不会保存中间的数据,序列化再反序列化之后null会被清除.顺序会变
例如
list.add(obj)
list.add(null)
list.add(obj)

7. 变量就直接放player身上,下线的时候统一从player身上拷一份到role身上入库
8. 如果把技能或者经验之类的东西,要判断ItemChange是否改变,没改变过的消息不需要同步给客户端
9. 实体内不能用内部类,不然会引死循环造成内存溢出