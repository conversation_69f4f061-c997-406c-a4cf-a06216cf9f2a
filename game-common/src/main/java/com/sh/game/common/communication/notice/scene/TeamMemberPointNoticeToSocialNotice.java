package com.sh.game.common.communication.notice.scene;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 通知社交模块，队伍成员坐标发生改变
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/8/19.
 */
@Setter
@Getter
@Notice
public class TeamMemberPointNoticeToSocialNotice  extends ProcessNotice {

    /**
     * 玩家id
     */
    private long  rid;

    private String name;
    /**
     * 玩家x
     */
    private int x;
    /**
     * 玩家y
     */
    private int y;



}
