package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018/6/13 15:11
 */
@Getter
@Notice
public class EquipUpdateNotice extends ProcessNotice {

    private long rid;

    private long actorId;

    private int pos;

    private int itemId;

    private long lid;

    private int durable;

    private int magicBloodStoneValue;

    public EquipUpdateNotice() {

    }

    public EquipUpdateNotice(long rid, long actorId, int pos, int itemId, long lid, int durable, int magicBloodStoneValue) {
        this.rid = rid;
        this.actorId = actorId;
        this.pos = pos;
        this.itemId = itemId;
        this.lid = lid;
        this.durable = durable;
        this.magicBloodStoneValue = magicBloodStoneValue;
    }

}
