package com.sh.game.common.communication.msg.system.roleMount;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回个人坐骑状态
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResRoleMountInfoMessage extends AbsProtostuffMessage {
  /**
   * 骑乘状态
   */
  private boolean ridingStatus;

  @Override
  public int getId() {
    return 363001;
  }

  public void setRidingStatus(boolean ridingStatus) {
    this.ridingStatus = ridingStatus;
  }

  public boolean getRidingStatus() {
    return this.ridingStatus;
  }
}
