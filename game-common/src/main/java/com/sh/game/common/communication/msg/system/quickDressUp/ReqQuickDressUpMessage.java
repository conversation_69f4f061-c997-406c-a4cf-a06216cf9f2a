package com.sh.game.common.communication.msg.system.quickDressUp;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求使用推荐装扮
 * 该文件由工具根据 quickDressUp.xml 文件自动生成，不可修改
 */
@RPC("toLogic")
public class ReqQuickDressUpMessage extends AbsProtostuffMessage {
  /**
   * 配置id
   */
  private int cid;

  @Override
  public int getId() {
    return 411001;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
