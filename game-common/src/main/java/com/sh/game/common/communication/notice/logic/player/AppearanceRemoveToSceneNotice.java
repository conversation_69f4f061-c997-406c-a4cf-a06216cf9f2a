package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/11/8.
 */
@Setter
@Getter
@Notice
public class AppearanceRemoveToSceneNotice extends ProcessNotice {
    private long rid;
    //装扮id
    private int appearanceId;

    public AppearanceRemoveToSceneNotice() {
    }

    public AppearanceRemoveToSceneNotice(long rid, int appearanceId) {
        this.rid = rid;
        this.appearanceId = appearanceId;
    }
}
