package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.communication.msg.system.recharge.bean.WelfareRechargeInfo;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 福利礼包信息消
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSendWelfareRechargeMessage extends AbsProtostuffMessage {
  /**
   * 福利礼包信息
   */
  private List<WelfareRechargeInfo> welfareInfo = new ArrayList<>();

  @Override
  public int getId() {
    return 39015;
  }

  public void setWelfareInfo(List<WelfareRechargeInfo> welfareInfo) {
    this.welfareInfo = welfareInfo;
  }

  public List<WelfareRechargeInfo> getWelfareInfo() {
    return this.welfareInfo;
  }
}
