package com.sh.game.common.communication.msg.system.identify;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求保存鉴定结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqIdentifySaveMessage extends AbsProtostuffMessage {
  /**
   * 装备uid
   */
  private long uid;

  @Override
  public int getId() {
    return 183002;
  }

  public void setUid(long uid) {
    this.uid = uid;
  }

  public long getUid() {
    return this.uid;
  }
}
