package com.sh.game.common.communication.msg.system.skillawake;

import com.sh.game.common.communication.msg.system.skillawake.bean.SkillAwakeBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回技能觉醒信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSkillAwakeInfoMessage extends AbsProtostuffMessage {
  /**
   * 技能觉醒套装配置id
   */
  private int suitId;

  /**
   * 技能觉醒信息
   */
  private List<SkillAwakeBean> skillAwakeBeanList = new ArrayList<>();

  @Override
  public int getId() {
    return 353002;
  }

  public void setSuitId(int suitId) {
    this.suitId = suitId;
  }

  public int getSuitId() {
    return this.suitId;
  }

  public void setSkillAwakeBeanList(List<SkillAwakeBean> skillAwakeBeanList) {
    this.skillAwakeBeanList = skillAwakeBeanList;
  }

  public List<SkillAwakeBean> getSkillAwakeBeanList() {
    return this.skillAwakeBeanList;
  }
}
