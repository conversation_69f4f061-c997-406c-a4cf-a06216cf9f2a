package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/7/10
 * @Desc : to do anything
 */
@Getter
@Setter
@Notice
public class UnionDonateNotice extends ProcessNotice {

    private long unionId;

    private long rid;

    private int fund;

}
