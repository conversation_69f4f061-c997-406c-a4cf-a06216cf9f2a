package com.sh.game.common.communication.msg.system.jifenstore.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class JiFenStoreItemBean extends KryoBean {

	/**
	 * 配置id
	 */
	private int cid;
	/**
	 * 购买次数
	 */
	private int count;
	/**
	 * 折扣,百分制
	 */
	private int discount;

	public int getCid() {
		return cid;
	}

	public void setCid(int cid) {
		this.cid = cid;
	}

		public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

		public int getDiscount() {
		return discount;
	}

	public void setDiscount(int discount) {
		this.discount = discount;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.cid = readInt(buf, false);
		this.count = readInt(buf, false);
		this.discount = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, cid, false);
		this.writeInt(buf, count, false);
		this.writeInt(buf, discount, false);
		return true;
	}
}
