package com.sh.game.common.communication.msg.system.itemUpgrade;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回道具升级信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResItemUpgradeMessage extends AbsProtostuffMessage {
  /**
   * cfg_equip_forging配置id
   */
  private int nextCfgId;

  @Override
  public int getId() {
    return 328002;
  }

  public void setNextCfgId(int nextCfgId) {
    this.nextCfgId = nextCfgId;
  }

  public int getNextCfgId() {
    return this.nextCfgId;
  }
}
