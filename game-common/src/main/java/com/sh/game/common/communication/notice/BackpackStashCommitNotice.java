package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.notice.Notice;
import com.sh.game.notice.TimeProcessNotice;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@Notice
public class BackpackStashCommitNotice extends TimeProcessNotice {

    private byte sourceProcessorId;

    private long roleId;

    private int action;

    private int flag;

    private boolean tips;

    private BackpackStash stash;

    private boolean noCallback;

    @Override
    public boolean first() {
        return true;
    }
}
