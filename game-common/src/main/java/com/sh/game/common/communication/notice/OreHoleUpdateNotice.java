package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;

/**
 * 挖矿状态改变
 */
@Getter
@Notice
public class OreHoleUpdateNotice extends ProcessNotice {

    private long rid;

    private boolean isDig;

    public OreHoleUpdateNotice() {
    }

    public OreHoleUpdateNotice(long rid, boolean isDig) {
        this.rid = rid;
        this.isDig = isDig;
    }

}
