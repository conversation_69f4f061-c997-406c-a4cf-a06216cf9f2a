package com.sh.game.common.communication.notice.common;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * Role 数据更新 Notice
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-09-17
 **/
@Getter
@Setter
@Notice
public class RoleUpDataToPlayerNotice extends ProcessNotice {

    private long roleId;

    public RoleUpDataToPlayerNotice() {
    }

    public RoleUpDataToPlayerNotice(long roleId) {
        this.roleId = roleId;
    }
}
