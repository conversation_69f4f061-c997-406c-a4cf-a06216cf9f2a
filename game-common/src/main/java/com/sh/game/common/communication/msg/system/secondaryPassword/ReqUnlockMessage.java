package com.sh.game.common.communication.msg.system.secondaryPassword;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 请求解锁
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqUnlockMessage extends AbsProtostuffMessage {
  /**
   * 密码
   */
  private String password = new String();

  @Override
  public int getId() {
    return 280005;
  }

  public void setPassword(String password) {
    this.password = password;
  }

  public String getPassword() {
    return this.password;
  }
}
