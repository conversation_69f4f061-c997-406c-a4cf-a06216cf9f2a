package com.sh.game.common.communication.msg.system.zhanling;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回领取战令任务奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ResZhanLingTaskRewardMessage extends AbsProtostuffMessage {
  /**
   * 战令任务表id      为0时表示一键领取
   */
  private int cid;

  /**
   * 是否领取成功
   */
  private boolean success;

  @Override
  public int getId() {
    return 402007;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }

  public void setSuccess(boolean success) {
    this.success = success;
  }

  public boolean getSuccess() {
    return this.success;
  }
}
