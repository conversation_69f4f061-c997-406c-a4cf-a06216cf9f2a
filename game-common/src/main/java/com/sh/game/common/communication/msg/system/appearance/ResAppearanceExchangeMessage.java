package com.sh.game.common.communication.msg.system.appearance;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 兑换返回
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResAppearanceExchangeMessage extends AbsProtostuffMessage {
  /**
   * 1表示成功，0表示失败
   */
  private int success;

  @Override
  public int getId() {
    return 200016;
  }

  public void setSuccess(int success) {
    this.success = success;
  }

  public int getSuccess() {
    return this.success;
  }
}
