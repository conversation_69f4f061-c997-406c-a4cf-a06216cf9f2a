package com.sh.game.common.communication.msg.system.monsterCard;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回怪物图鉴信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMonsterCardInfoMessage extends AbsProtostuffMessage {
  /**
   * 已解锁的怪物图鉴表id
   */
  private List<Integer> unlockedHandbookCids = new ArrayList<>();

  /**
   * 已领取的怪物图鉴进度表id
   */
  private List<Integer> requiredRewardCids = new ArrayList<>();

  /**
   * 怪物图鉴进度列表 key: monsterCfgid  value: 数量
   */
  private List<CommonKeyValueBean> monsterCardProgress = new ArrayList<>();

  @Override
  public int getId() {
    return 316011;
  }

  public void setUnlockedHandbookCids(List<Integer> unlockedHandbookCids) {
    this.unlockedHandbookCids = unlockedHandbookCids;
  }

  public List<Integer> getUnlockedHandbookCids() {
    return this.unlockedHandbookCids;
  }

  public void setRequiredRewardCids(List<Integer> requiredRewardCids) {
    this.requiredRewardCids = requiredRewardCids;
  }

  public List<Integer> getRequiredRewardCids() {
    return this.requiredRewardCids;
  }

  public void setMonsterCardProgress(List<CommonKeyValueBean> monsterCardProgress) {
    this.monsterCardProgress = monsterCardProgress;
  }

  public List<CommonKeyValueBean> getMonsterCardProgress() {
    return this.monsterCardProgress;
  }
}
