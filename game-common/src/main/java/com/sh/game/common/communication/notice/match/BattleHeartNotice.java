package com.sh.game.common.communication.notice.match;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/10/8 9:37
 */
@Getter
@Setter
@Notice
public class BattleHeartNotice extends ProcessNotice {

    /**
     * 服务器id
     */
    private int serverId;
    /**
     * 比赛数
     */
    private int matchCount;
    /**
     * 玩家数
     */
    private int playerCount;
}
