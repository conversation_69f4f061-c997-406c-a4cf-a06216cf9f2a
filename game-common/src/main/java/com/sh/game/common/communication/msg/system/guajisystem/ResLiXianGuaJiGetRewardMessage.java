package com.sh.game.common.communication.msg.system.guajisystem;

import com.sh.game.common.communication.msg.system.guajisystem.bean.RewardBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 领取成功返回已领取的列表
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResLiXianGuaJiGetRewardMessage extends AbsProtostuffMessage {
  /**
   * 已领取的奖励信息
   */
  private List<RewardBean> rewardList = new ArrayList<>();

  @Override
  public int getId() {
    return 379004;
  }

  public void setRewardList(List<RewardBean> rewardList) {
    this.rewardList = rewardList;
  }

  public List<RewardBean> getRewardList() {
    return this.rewardList;
  }
}
