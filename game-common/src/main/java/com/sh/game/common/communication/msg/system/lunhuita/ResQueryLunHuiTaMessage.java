package com.sh.game.common.communication.msg.system.lunhuita;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回当前轮回塔状态信息, 当有新充值次数或每日重置则自动推送一次
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResQueryLunHuiTaMessage extends AbsProtostuffMessage {
  /**
   * 已通关层数
   */
  private int layer;

  /**
   * 剩余挑战次数
   */
  private int count;

  /**
   * 最后一层奖历的领取状态(只有通关后才可以)
   */
  private int recState;

  /**
   * 当天可挑战总次数
   */
  private int totalCount;

  /**
   * 当天挑战所在大陆
   */
  private int landID;

  @Override
  public int getId() {
    return 325002;
  }

  public void setLayer(int layer) {
    this.layer = layer;
  }

  public int getLayer() {
    return this.layer;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }

  public void setRecState(int recState) {
    this.recState = recState;
  }

  public int getRecState() {
    return this.recState;
  }

  public void setTotalCount(int totalCount) {
    this.totalCount = totalCount;
  }

  public int getTotalCount() {
    return this.totalCount;
  }

  public void setLandID(int landID) {
    this.landID = landID;
  }

  public int getLandID() {
    return this.landID;
  }
}
