package com.sh.game.common.communication.msg.system.chongWuEquip;

import com.sh.game.common.communication.msg.abc.bean.CommonItemBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回鉴定信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResChongWuEquipJianDingMessage extends AbsProtostuffMessage {
  /**
   * 宠物装备鉴定信息
   */
  private CommonItemBean itemBean = new CommonItemBean();

  @Override
  public int getId() {
    return 357005;
  }

  public void setItemBean(CommonItemBean itemBean) {
    this.itemBean = itemBean;
  }

  public CommonItemBean getItemBean() {
    return this.itemBean;
  }
}
