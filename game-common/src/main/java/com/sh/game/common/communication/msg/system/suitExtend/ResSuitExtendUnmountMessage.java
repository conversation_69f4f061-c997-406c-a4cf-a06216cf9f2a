package com.sh.game.common.communication.msg.system.suitExtend;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>返回继承套装移除结果</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ResSuitExtendUnmountMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 183016;
	}
	
	/**
	 * 移除是否成功1：成功，0：失败
	 */
	private int sucess;
	/**
	 * 被卸灵的装备uid
	 */
	private long sourceItemUid;
	/**
	 * 移除后获得的装备uid
	 */
	private long resultItemUid;

	public int getSucess() {
		return sucess;
	}

	public void setSucess(int sucess) {
		this.sucess = sucess;
	}

		public long getSourceItemUid() {
		return sourceItemUid;
	}

	public void setSourceItemUid(long sourceItemUid) {
		this.sourceItemUid = sourceItemUid;
	}

		public long getResultItemUid() {
		return resultItemUid;
	}

	public void setResultItemUid(long resultItemUid) {
		this.resultItemUid = resultItemUid;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.sucess = readInt(buf, false);
		this.sourceItemUid = readLong(buf);
		this.resultItemUid = readLong(buf);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, sucess, false);
		this.writeLong(buf, sourceItemUid);
		this.writeLong(buf, resultItemUid);
		return true;
	}
}
