package com.sh.game.common.communication.msg.system.chongWuEquip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求宠物装备强化
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqChongWuEquipQiangHuaMessage extends AbsProtostuffMessage {
  /**
   * 部位
   */
  private int pos;

  @Override
  public int getId() {
    return 357001;
  }

  public void setPos(int pos) {
    this.pos = pos;
  }

  public int getPos() {
    return this.pos;
  }
}
