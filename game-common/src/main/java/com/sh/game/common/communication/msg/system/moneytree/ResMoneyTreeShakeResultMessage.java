package com.sh.game.common.communication.msg.system.moneytree;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回摇动结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMoneyTreeShakeResultMessage extends AbsProtostuffMessage {
  /**
   * 倍率
   */
  private int rate;

  @Override
  public int getId() {
    return 362007;
  }

  public void setRate(int rate) {
    this.rate = rate;
  }

  public int getRate() {
    return this.rate;
  }
}
