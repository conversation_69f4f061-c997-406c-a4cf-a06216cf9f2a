package com.sh.game.common.communication.msg.system.handbook;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取图鉴进度奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqHandbookRewardMessage extends AbsProtostuffMessage {
  /**
   * 装备图鉴奖励表id(图鉴组)
   */
  private int cid;

  @Override
  public int getId() {
    return 314003;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
