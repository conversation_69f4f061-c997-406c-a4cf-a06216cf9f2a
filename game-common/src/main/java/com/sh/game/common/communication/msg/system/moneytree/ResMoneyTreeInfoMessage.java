package com.sh.game.common.communication.msg.system.moneytree;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回摇钱树个人信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMoneyTreeInfoMessage extends AbsProtostuffMessage {
  /**
   * 财神特权, key: 特权id value: 过期时间戳(s),-1为永久
   */
  private List<CommonKeyValueBean> monthCard = new ArrayList<>();

  /**
   * 今日已免费摇动次数
   */
  private int shakeFreeCount;

  /**
   * 今日已摇动次数
   */
  private int shakeCount;

  /**
   * 已领取奖励id列表
   */
  private List<Integer> rewardList = new ArrayList<>();

  /**
   * 已领取额外奖励id列表
   */
  private List<Integer> extraRewardList = new ArrayList<>();

  @Override
  public int getId() {
    return 362002;
  }

  public void setMonthCard(List<CommonKeyValueBean> monthCard) {
    this.monthCard = monthCard;
  }

  public List<CommonKeyValueBean> getMonthCard() {
    return this.monthCard;
  }

  public void setShakeFreeCount(int shakeFreeCount) {
    this.shakeFreeCount = shakeFreeCount;
  }

  public int getShakeFreeCount() {
    return this.shakeFreeCount;
  }

  public void setShakeCount(int shakeCount) {
    this.shakeCount = shakeCount;
  }

  public int getShakeCount() {
    return this.shakeCount;
  }

  public void setRewardList(List<Integer> rewardList) {
    this.rewardList = rewardList;
  }

  public List<Integer> getRewardList() {
    return this.rewardList;
  }

  public void setExtraRewardList(List<Integer> extraRewardList) {
    this.extraRewardList = extraRewardList;
  }

  public List<Integer> getExtraRewardList() {
    return this.extraRewardList;
  }
}
