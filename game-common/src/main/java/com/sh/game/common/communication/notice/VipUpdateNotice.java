package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018/6/13 15:12
 */
@Getter
@Notice
public class VipUpdateNotice extends ProcessNotice {

    private long rid;

    private int vipLevel;


    public VipUpdateNotice() {

    }

    public VipUpdateNotice(long rid, int vipLevel) {
        this.rid = rid;
        this.vipLevel = vipLevel;
    }

}
