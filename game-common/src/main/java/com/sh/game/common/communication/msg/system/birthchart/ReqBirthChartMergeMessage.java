package com.sh.game.common.communication.msg.system.birthchart;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求命格穿戴
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqBirthChartMergeMessage extends AbsProtostuffMessage {
  /**
   * 道具背包
   */
  private int sourceWhere;

  /**
   * 道具位置
   */
  private int sourceIndex;

  /**
   * 目标背包
   */
  private int targetWhere;

  /**
   * 目标位置
   */
  private int targetIndex;

  /**
   * 行为
   */
  private int logAction;

  @Override
  public int getId() {
    return 380007;
  }

  public void setSourceWhere(int sourceWhere) {
    this.sourceWhere = sourceWhere;
  }

  public int getSourceWhere() {
    return this.sourceWhere;
  }

  public void setSourceIndex(int sourceIndex) {
    this.sourceIndex = sourceIndex;
  }

  public int getSourceIndex() {
    return this.sourceIndex;
  }

  public void setTargetWhere(int targetWhere) {
    this.targetWhere = targetWhere;
  }

  public int getTargetWhere() {
    return this.targetWhere;
  }

  public void setTargetIndex(int targetIndex) {
    this.targetIndex = targetIndex;
  }

  public int getTargetIndex() {
    return this.targetIndex;
  }

  public void setLogAction(int logAction) {
    this.logAction = logAction;
  }

  public int getLogAction() {
    return this.logAction;
  }
}
