package com.sh.game.common.communication.msg.system.identify;

import com.sh.game.common.communication.msg.abc.bean.RandAttributeBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 鉴定返回
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResIdentifyEquipMessage extends AbsProtostuffMessage {
  /**
   * 装备uid
   */
  private long uid;

  /**
   * 本次鉴定结果
   */
  private List<RandAttributeBean> attributes = new ArrayList<>();

  @Override
  public int getId() {
    return 183011;
  }

  public void setUid(long uid) {
    this.uid = uid;
  }

  public long getUid() {
    return this.uid;
  }

  public void setAttributes(List<RandAttributeBean> attributes) {
    this.attributes = attributes;
  }

  public List<RandAttributeBean> getAttributes() {
    return this.attributes;
  }
}
