package com.sh.game.common.communication.msg.system.mingwang;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回江湖名望信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMingWangMessage extends AbsProtostuffMessage {
  /**
   * 江湖名望,值未cfg_ih_prestige的id
   */
  private int mingWang;

  @Override
  public int getId() {
    return 315002;
  }

  public void setMingWang(int mingWang) {
    this.mingWang = mingWang;
  }

  public int getMingWang() {
    return this.mingWang;
  }
}
