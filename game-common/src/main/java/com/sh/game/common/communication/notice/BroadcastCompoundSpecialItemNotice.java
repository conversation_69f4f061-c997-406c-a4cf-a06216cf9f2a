package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/6/30
 * @Desc : to do anything
 */
@Getter
@Setter
@Notice
public class BroadcastCompoundSpecialItemNotice extends ProcessNotice {

    private long rid;

    private String roleName;

    private int itemId;

    private int compoundTime;

    private int originHostId;

}
