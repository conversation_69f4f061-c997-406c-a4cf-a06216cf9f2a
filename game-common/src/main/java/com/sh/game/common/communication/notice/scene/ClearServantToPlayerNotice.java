package com.sh.game.common.communication.notice.scene;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2021-08-27
 **/
@Getter
@Setter
@Notice
public class ClearServantToPlayerNotice extends ProcessNotice {
    private long rid;

    public ClearServantToPlayerNotice() {
    }

    public ClearServantToPlayerNotice(long rid) {
        this.rid = rid;
    }
}
