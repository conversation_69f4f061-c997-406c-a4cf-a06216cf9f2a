package com.sh.game.common.communication.msg.system.magicweapon;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.abc.bean.MagicWeaponBlendBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>返回法器融合分离后装备信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

//@RPC("toClient")
//public class ResMagicWeaponChangeInfoMessage extends AbstractMessage {
//
//	@Override
//	public int getId() {
//		return 341006;
//	}
//
//	/**
//	 * 装备道具唯一id
//	 */
//	private long equipId;
//	/**
//	 * 镶嵌融合的法宝
//	 */
//	private List<MagicWeaponBlendBean> magicWeaponBlendList = new ArrayList<>();
//
//	public long getEquipId() {
//		return equipId;
//	}
//
//	public void setEquipId(long equipId) {
//		this.equipId = equipId;
//	}
//
//	public List<MagicWeaponBlendBean> getMagicWeaponBlendList() {
//		return magicWeaponBlendList;
//	}
//
//	public void setMagicWeaponBlendList(List<MagicWeaponBlendBean> magicWeaponBlendList) {
//		this.magicWeaponBlendList = magicWeaponBlendList;
//	}
//
//	@Override
//	public boolean read(KryoInput buf) {
//
//		this.equipId = readLong(buf);
//		int magicWeaponBlendListLength = readShort(buf);
//		for (int magicWeaponBlendListI = 0; magicWeaponBlendListI < magicWeaponBlendListLength; magicWeaponBlendListI++) {
//			if (readByte(buf) == 0) {
//				this.magicWeaponBlendList.add(null);
//			} else {
//				MagicWeaponBlendBean magicWeaponBlendBean = new MagicWeaponBlendBean();
//				magicWeaponBlendBean.read(buf);
//				this.magicWeaponBlendList.add(magicWeaponBlendBean);
//			}
//		}
//		return true;
//	}
//
//	@Override
//	public boolean write(KryoOutput buf) {
//
//		this.writeLong(buf, equipId);
//		writeShort(buf, this.magicWeaponBlendList.size());
//		for (int magicWeaponBlendListI = 0; magicWeaponBlendListI < this.magicWeaponBlendList.size(); magicWeaponBlendListI++) {
//			this.writeBean(buf, this.magicWeaponBlendList.get(magicWeaponBlendListI));
//		}
//		return true;
//	}
//}
