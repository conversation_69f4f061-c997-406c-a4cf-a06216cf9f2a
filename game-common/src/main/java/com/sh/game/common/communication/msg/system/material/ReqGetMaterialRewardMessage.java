package com.sh.game.common.communication.msg.system.material;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取材料副本副本奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqGetMaterialRewardMessage extends AbsProtostuffMessage {
  /**
   * 类型 0:普通领取 1:双倍领取
   */
  private int type;

  /**
   * 副本id
   */
  private int duplicateId;

  @Override
  public int getId() {
    return 179002;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setDuplicateId(int duplicateId) {
    this.duplicateId = duplicateId;
  }

  public int getDuplicateId() {
    return this.duplicateId;
  }
}
