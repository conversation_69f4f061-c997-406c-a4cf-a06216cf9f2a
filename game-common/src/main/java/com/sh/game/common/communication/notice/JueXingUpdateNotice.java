package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * 觉醒等级升级
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-03-01
 **/
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Notice
public class JueXingUpdateNotice extends ProcessNotice {
    private long roleId;
    private Map<Integer, Integer> juexingLevel;
}
