package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 日常活动状态变更通知
 */

@Getter
@Setter
@Notice
public class DailyPeriodStatusChangedNotice extends ProcessNotice {

    /**
     * 活动类型
     */
    private int dailyType;

    /**
     * 活动编号（结束活动编号为0）
     */
    private int scheduleId;

    /**
     * 是否开始
     */
    private boolean inPeriod;

    /**
     * 活动开始参数
     */
    private Object param;


}
