package com.sh.game.common.communication.msg.system.clientdata;

import com.sh.game.common.communication.msg.system.clientdata.bean.ClientStringDataBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

@RPC("toClient")
public class ResClientStringDataMessage extends AbsProtostuffMessage {
  private List<ClientStringDataBean> clientStringDataBean = new ArrayList<>();

  @Override
  public int getId() {
    return 391008;
  }

  public void setClientStringDataBean(List<ClientStringDataBean> clientStringDataBean) {
    this.clientStringDataBean = clientStringDataBean;
  }

  public List<ClientStringDataBean> getClientStringDataBean() {
    return this.clientStringDataBean;
  }
}
