package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * ATO：yumo<br>;
 * 时间：2021/1/16 13:09<br>;
 * 版本：1.0<br>;
 * 描述：
 */
@Getter
@Setter
@Notice
public class MapKeepItemNotice extends ProcessNotice {
    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 当前地图需要消耗道具拥有总数
     */
    private int totalOwn;
}
