package com.sh.game.common.communication.msg.system.rewardinfo;

import com.sh.game.common.communication.msg.system.rewardinfo.bean.ItemBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回奖励信息
 * 该文件由工具根据 rewardinfo.xml 文件自动生成，不可修改
 */
@RPC("toClient")
public class ResRewardInfoMessage extends AbsProtostuffMessage {
  /**
   *  类型
   */
  private int type;

  private List<ItemBean> itemBean = new ArrayList<>();

  @Override
  public int getId() {
    return 397001;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setItemBean(List<ItemBean> itemBean) {
    this.itemBean = itemBean;
  }

  public List<ItemBean> getItemBean() {
    return this.itemBean;
  }
}
