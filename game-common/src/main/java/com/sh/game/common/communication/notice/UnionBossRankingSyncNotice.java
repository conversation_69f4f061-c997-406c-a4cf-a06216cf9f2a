package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@Notice
public class UnionBossRankingSyncNotice extends ProcessNotice {

    private Map<Long, Integer> rankings;
    /**
     * 竞拍道具
     */
    Map<Long, List<Item>> unionAuctionMap;
    /**
     * 竞拍积分
     */
    Map<Long, Map<Long, Long>> unionScoreMap;
}
