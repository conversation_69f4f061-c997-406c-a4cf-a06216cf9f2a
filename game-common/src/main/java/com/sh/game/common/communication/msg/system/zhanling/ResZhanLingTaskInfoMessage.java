package com.sh.game.common.communication.msg.system.zhanling;

import com.sh.game.common.communication.msg.system.task.bean.TaskDataBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回战令任务信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResZhanLingTaskInfoMessage extends AbsProtostuffMessage {
  /**
   * 活动id
   */
  private int activityId;

  /**
   * 正在进行的战令任务
   */
  private List<TaskDataBean> taskBean = new ArrayList<>();

  @Override
  public int getId() {
    return 402009;
  }

  public void setActivityId(int activityId) {
    this.activityId = activityId;
  }

  public int getActivityId() {
    return this.activityId;
  }

  public void setTaskBean(List<TaskDataBean> taskBean) {
    this.taskBean = taskBean;
  }

  public List<TaskDataBean> getTaskBean() {
    return this.taskBean;
  }
}
