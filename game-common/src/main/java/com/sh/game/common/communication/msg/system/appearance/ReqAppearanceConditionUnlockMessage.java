package com.sh.game.common.communication.msg.system.appearance;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 时装解锁
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqAppearanceConditionUnlockMessage extends AbsProtostuffMessage {
  /**
   * 解锁时装的id
   */
  private int fashionId;

  @Override
  public int getId() {
    return 200017;
  }

  public void setFashionId(int fashionId) {
    this.fashionId = fashionId;
  }

  public int getFashionId() {
    return this.fashionId;
  }
}
