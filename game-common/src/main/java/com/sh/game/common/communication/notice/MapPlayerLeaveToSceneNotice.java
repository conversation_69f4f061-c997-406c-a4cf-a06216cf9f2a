package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 *  通知玩家已经离开场景
 */
@Notice
@Getter
@Setter
public class MapPlayerLeaveToSceneNotice extends ProcessNotice {

    private long roleId;

    private long mapId;

    private int mapCfgId;

    private boolean erase;

    /**
     * 是否登出的离开场景
     */
    private boolean isLogout;

    /**
     * 是否留下假人
     */
    private boolean fakePlayer;
}
