package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/6/17
 * @Desc : to do anything
 */
@Getter
@Notice
public class SwitchFightModuleNotice extends ProcessNotice {

    private long rid;

    private int fightModel;

    public SwitchFightModuleNotice() {
    }

    public SwitchFightModuleNotice(long rid, int fightModel) {
        this.rid = rid;
        this.fightModel = fightModel;
    }
}
