package com.sh.game.common.communication.msg.system.birthchart;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回猎命
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResBirthChartDrawMessage extends AbsProtostuffMessage {
  /**
   * 本次获得的奖励列表,key:道具配置id,value:数量
   */
  private List<CommonKeyValueBean> rewardList = new ArrayList<>();

  @Override
  public int getId() {
    return 380004;
  }

  public void setRewardList(List<CommonKeyValueBean> rewardList) {
    this.rewardList = rewardList;
  }

  public List<CommonKeyValueBean> getRewardList() {
    return this.rewardList;
  }
}
