package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 转职通知
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-08-27
 **/
@Notice
@Setter
@Getter
public class ChangeCareerToSceneNotice extends ProcessNotice {
    private long rid;

    /**
     * 目标职业
     */
    private int career;

    public ChangeCareerToSceneNotice() {
    }

    public ChangeCareerToSceneNotice(long rid, int career) {
        this.rid = rid;
        this.career = career;
    }
}
