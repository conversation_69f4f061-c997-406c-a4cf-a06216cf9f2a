package com.sh.game.common.communication.notice.match;

import com.sh.game.common.communication.msg.pvp.bean.BattlePlayerBean;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/8/13.
 */
@Notice
@Getter
@Setter
public class MatchSuccessToLogicNotice extends ProcessNotice {

    /**
     * 匹配组列表
     */
    private List<Long> groupId = new ArrayList<>();
    /**
     * 战斗服id
     */
    private int hostId;
    /**
     * pvp类型
     */
    private int pvpType;
    /**
     * 比赛服IP地址
     */
    private String ip;
    /**
     * 比赛服端口
     */
    private int port;
    /**
     * 地图id
     */
    private int mapId;
    /**
     * 地图配置id
     */
    private int cfgId;
    private long mapInstId;
    /**
     * 匹配的玩家列表
     */
    private List<BattlePlayerBean> playerList = new ArrayList<>();

}
