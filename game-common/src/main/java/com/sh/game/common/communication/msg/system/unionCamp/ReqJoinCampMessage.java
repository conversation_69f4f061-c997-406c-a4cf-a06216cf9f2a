package com.sh.game.common.communication.msg.system.unionCamp;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求加入阵营
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqJoinCampMessage extends AbsProtostuffMessage {
  /**
   * 待加入的阵营类型
   */
  private int campType;

  /**
   * 是否清除cd
   */
  private boolean isClearCd;

  @Override
  public int getId() {
    return 365003;
  }

  public void setCampType(int campType) {
    this.campType = campType;
  }

  public int getCampType() {
    return this.campType;
  }

  public void setIsClearCd(boolean isClearCd) {
    this.isClearCd = isClearCd;
  }

  public boolean getIsClearCd() {
    return this.isClearCd;
  }
}
