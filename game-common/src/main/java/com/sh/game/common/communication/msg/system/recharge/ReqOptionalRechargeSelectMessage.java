package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求自选充值商品挑选
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqOptionalRechargeSelectMessage extends AbsProtostuffMessage {
  /**
   * 配置id
   */
  private int cfgId;

  /**
   * 奖励编号 1 or 2
   */
  private int rewardIndex;

  /**
   * 道具编号 1,2...
   */
  private int itemIndex;

  @Override
  public int getId() {
    return 39016;
  }

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }

  public void setRewardIndex(int rewardIndex) {
    this.rewardIndex = rewardIndex;
  }

  public int getRewardIndex() {
    return this.rewardIndex;
  }

  public void setItemIndex(int itemIndex) {
    this.itemIndex = itemIndex;
  }

  public int getItemIndex() {
    return this.itemIndex;
  }
}
