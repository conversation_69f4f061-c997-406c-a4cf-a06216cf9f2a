package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2020/4/27
 * @Desc : to do anything
 */
@Getter
@Setter
@Notice
public class RobotInfoUpdateNotice extends ProcessNotice {

    private long robotUid;

    private int level;

    private Map<Integer, Integer> equipMap;

    private Map<Integer, Integer> fashionMap;

    private Attribute attribute;
}
