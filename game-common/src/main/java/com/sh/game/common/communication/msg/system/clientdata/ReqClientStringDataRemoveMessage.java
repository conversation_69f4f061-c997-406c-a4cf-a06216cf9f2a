package com.sh.game.common.communication.msg.system.clientdata;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;
import java.util.ArrayList;
import java.util.List;

@RPC("toLogic")
public class ReqClientStringDataRemoveMessage extends AbsProtostuffMessage {
  private List<String> key = new ArrayList<>();

  @Override
  public int getId() {
    return 391007;
  }

  public void setKey(List<String> key) {
    this.key = key;
  }

  public List<String> getKey() {
    return this.key;
  }
}
