package com.sh.game.common.communication.msg.system.identify;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回继承套装移除结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSuitExtendUnmountMessage extends AbsProtostuffMessage {
  /**
   * 移除是否成功1：成功，0：失败
   */
  private int sucess;

  /**
   * 被卸灵的装备uid
   */
  private long sourceItemUid;

  /**
   * 移除后获得的装备uid
   */
  private long resultItemUid;

  @Override
  public int getId() {
    return 183016;
  }

  public void setSucess(int sucess) {
    this.sucess = sucess;
  }

  public int getSucess() {
    return this.sucess;
  }

  public void setSourceItemUid(long sourceItemUid) {
    this.sourceItemUid = sourceItemUid;
  }

  public long getSourceItemUid() {
    return this.sourceItemUid;
  }

  public void setResultItemUid(long resultItemUid) {
    this.resultItemUid = resultItemUid;
  }

  public long getResultItemUid() {
    return this.resultItemUid;
  }
}
