package com.sh.game.common.communication.msg.system.dunPaiShengJi;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求盾牌升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqDunPaiUpMessage extends AbsProtostuffMessage {
  /**
   * 升级类型,1时装升级，2盾牌升级
   */
  private int type;

  @Override
  public int getId() {
    return 349001;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
