package com.sh.game.common.communication.msg.system.chat.bean;

import com.sh.game.common.communication.msg.abc.bean.CommonSlotBean;

import java.util.ArrayList;
import java.util.List;

public class ChatSourceBean {
  /**
   * 发送者id
   */
  private long sendUid;

  /**
   * 发送者名字
   */
  private String sendName = new String();


  /**
   * 发送者转生id
   */
  private int zhuanshengId;

  /**
   * 发送者职业
   */
  private int career;

  /**
   * 时装
   */
  private List<CommonSlotBean> fashions = new ArrayList<>();

  /**
   * 发送者等级
   */
  private int level;

  /**
   * 发送者vip等级
   */
  private int vipLevel;

  public void setSendUid(long sendUid) {
    this.sendUid = sendUid;
  }

  public long getSendUid() {
    return this.sendUid;
  }

  public void setSendName(String sendName) {
    this.sendName = sendName;
  }

  public String getSendName() {
    return this.sendName;
  }

  public void setZhuanshengId(int zhuanshengId) {
    this.zhuanshengId = zhuanshengId;
  }

  public int getZhuanshengId() {
    return this.zhuanshengId;
  }

  public void setCareer(int career) {
    this.career = career;
  }

  public int getCareer() {
    return this.career;
  }

  public void setFashions(List<CommonSlotBean> fashions) {
    this.fashions = fashions;
  }

  public List<CommonSlotBean> getFashions() {
    return this.fashions;
  }

  public int getLevel() {
    return this.level;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getVipLevel() {
    return this.vipLevel;
  }

  public void setVipLevel(int vipLevel) {
    this.vipLevel = vipLevel;
  }
}
