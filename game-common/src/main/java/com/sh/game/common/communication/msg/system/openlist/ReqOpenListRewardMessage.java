package com.sh.game.common.communication.msg.system.openlist;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqOpenListRewardMessage extends AbsProtostuffMessage {
  /**
   *  openList表id
   */
  private int cfgId;

  @Override
  public int getId() {
    return 395003;
  }

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }
}
