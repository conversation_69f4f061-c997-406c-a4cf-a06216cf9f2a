package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;


@Getter
@Setter
@Notice
public class AppendAuctionNotice extends ProcessNotice {

    private long unionId;

    private List<Item> appends = new ArrayList<>();

    private long[] shares;
}
