package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回返利充值信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResBackRechargeInfoMessage extends AbsProtostuffMessage {
  /**
   * 活动id
   */
  private int activityId;

  /**
   * 领取状态 0 不可领 1 可领 2 已领取
   */
  private int state;

  @Override
  public int getId() {
    return 39013;
  }

  public void setActivityId(int activityId) {
    this.activityId = activityId;
  }

  public int getActivityId() {
    return this.activityId;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getState() {
    return this.state;
  }
}
