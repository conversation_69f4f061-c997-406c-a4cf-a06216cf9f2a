package com.sh.game.common.communication.msg.system.magiccircle;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求法阵升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqMagicCircleUpLevelMessage extends AbsProtostuffMessage {
  /**
   * 法阵类型
   */
  private int type;

  @Override
  public int getId() {
    return 333001;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
