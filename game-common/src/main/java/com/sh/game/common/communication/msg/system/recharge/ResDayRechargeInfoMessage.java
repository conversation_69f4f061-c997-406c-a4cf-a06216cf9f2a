package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.communication.msg.system.recharge.bean.DayPayInfo;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 发送每日充值信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResDayRechargeInfoMessage extends AbsProtostuffMessage {
  /**
   * 每日充值信息
   */
  private List<DayPayInfo> infos = new ArrayList<>();

  /**
   * 当日充值总数
   */
  private int dayTotalRecharge;

  /**
   * 总充值金钱（rmb）
   */
  private int lifeTotalRecharge;

  @Override
  public int getId() {
    return 39006;
  }

  public void setInfos(List<DayPayInfo> infos) {
    this.infos = infos;
  }

  public List<DayPayInfo> getInfos() {
    return this.infos;
  }

  public void setDayTotalRecharge(int dayTotalRecharge) {
    this.dayTotalRecharge = dayTotalRecharge;
  }

  public int getDayTotalRecharge() {
    return this.dayTotalRecharge;
  }

  public void setLifeTotalRecharge(int lifeTotalRecharge) {
    this.lifeTotalRecharge = lifeTotalRecharge;
  }

  public int getLifeTotalRecharge() {
    return this.lifeTotalRecharge;
  }
}
