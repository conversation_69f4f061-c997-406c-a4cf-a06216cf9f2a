package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.map.RankDTO;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 沙巴克结束处理数据
 */
@Getter
@Setter
@Notice
public class ShaBaKeRecordDataNotice extends ProcessNotice {

    private long unionId;

    private List<RankDTO> rankList;

    private int dailyType;

    private String winUnionName;

}
