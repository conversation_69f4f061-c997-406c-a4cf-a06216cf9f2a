package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;


/**
 * 队伍变更
 */

@Getter
@Setter
@Notice
public class TeamChangeNotice extends ProcessNotice {

    /**
     * 玩家编号
     */
    private long rid;

    /**
     * 队伍编号
     */
    private long teamID;

    /**
     * 队伍类型
     */
    private int teamType;

}
