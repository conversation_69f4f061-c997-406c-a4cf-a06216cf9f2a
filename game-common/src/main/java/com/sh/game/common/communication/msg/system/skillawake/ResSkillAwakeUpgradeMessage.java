package com.sh.game.common.communication.msg.system.skillawake;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回技能觉醒升级信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSkillAwakeUpgradeMessage extends AbsProtostuffMessage {
  /**
   * 技能配置id
   */
  private int skillConfigId;

  /**
   * 觉醒等级
   */
  private int level;

  @Override
  public int getId() {
    return 353004;
  }

  public void setSkillConfigId(int skillConfigId) {
    this.skillConfigId = skillConfigId;
  }

  public int getSkillConfigId() {
    return this.skillConfigId;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getLevel() {
    return this.level;
  }
}
