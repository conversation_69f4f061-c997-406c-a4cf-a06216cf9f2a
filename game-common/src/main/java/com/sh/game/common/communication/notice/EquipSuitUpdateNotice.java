package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;

@Setter
@Getter
@Notice
@NoArgsConstructor
@AllArgsConstructor
public class EquipSuitUpdateNotice extends ProcessNotice {
    long rid;
    long actorId;
    Set<Integer> suitSet;
}
