package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Notice
public class CreateMonsterNotice extends ProcessNotice {

    /**
     * 玩家编号
     */
    private long rid;

    /**
     * 怪物编号
     */
    private int mid;

    /**
     * 怪物数量
     */
    private int count;

    /**
     * 是否需要布怪器
     */
    private boolean withSpawn;

    /**
     * 是否安全区检测
     */
    private boolean needSafe;

    /**
     * 是否需要公告
     */
    private boolean announce;

    /**
     * 是否需要设置归属
     */
    private boolean needOwner;

    private int overTime;

    /**
     * 是否偏移，1为偏移
     */
    private int pianyi;
}
