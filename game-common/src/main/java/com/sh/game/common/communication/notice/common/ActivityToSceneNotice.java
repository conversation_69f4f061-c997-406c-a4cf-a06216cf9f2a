package com.sh.game.common.communication.notice.common;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 活动数据同步给场景
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/10/9.
 */
@Getter
@Setter
@Notice
public class ActivityToSceneNotice extends ProcessNotice {

    /**
     * 活动刷怪时间减半
     */
    private boolean activitHALF;
}
