package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.communication.msg.system.recharge.bean.RechargeNumberBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回玩家充值数据
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResGetRechargeInfoMessage extends AbsProtostuffMessage {
  /**
   * 每日充值数（游戏币）
   */
  private long rechargedDaily;

  /**
   * 每日充值数（游戏币）
   */
  private long rechargedTotal;

  /**
   * 总共充值次数
   */
  private int rechargedCount;

  /**
   * 每档充值次数，累计充值计数
   */
  private List<RechargeNumberBean> rechargeNumberBean = new ArrayList<>();

  /**
   * 每档充值计数 根据限制类型来确定
   */
  private List<RechargeNumberBean> rechargeCountBean = new ArrayList<>();

  @Override
  public int getId() {
    return 39002;
  }

  public void setRechargedDaily(long rechargedDaily) {
    this.rechargedDaily = rechargedDaily;
  }

  public long getRechargedDaily() {
    return this.rechargedDaily;
  }

  public void setRechargedTotal(long rechargedTotal) {
    this.rechargedTotal = rechargedTotal;
  }

  public long getRechargedTotal() {
    return this.rechargedTotal;
  }

  public void setRechargedCount(int rechargedCount) {
    this.rechargedCount = rechargedCount;
  }

  public int getRechargedCount() {
    return this.rechargedCount;
  }

  public void setRechargeNumberBean(List<RechargeNumberBean> rechargeNumberBean) {
    this.rechargeNumberBean = rechargeNumberBean;
  }

  public List<RechargeNumberBean> getRechargeNumberBean() {
    return this.rechargeNumberBean;
  }

  public void setRechargeCountBean(List<RechargeNumberBean> rechargeCountBean) {
    this.rechargeCountBean = rechargeCountBean;
  }

  public List<RechargeNumberBean> getRechargeCountBean() {
    return this.rechargeCountBean;
  }
}
