package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@Notice
public class CompleteDuplicateNotice extends ProcessNotice {
    /**
     * 玩家id
     */
    long rid;
    /**
     * 关卡唯一标识id
     */
    int duplicateID;
    /**
     * 是否成功通关
     */
    boolean success;
    /**
     * 关卡号
     */
    int barrierNo;
    /**
     * 关卡通关耗时，单位秒
     */
    int completeTime;
    /**
     * 业务参数，暂时没有用到
     */
    Object param;
}
