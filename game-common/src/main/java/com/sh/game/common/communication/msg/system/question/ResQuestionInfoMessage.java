package com.sh.game.common.communication.msg.system.question;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.system.question.bean.QuestionBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>返回题目信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class ResQuestionInfoMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 64001;
	}
	
	/**
	 * 触发组
	 */
	private int type;
	/**
	 * 答题结束时间戳（秒）
	 */
	private int daTiEndTime;
	/**
	 * 问题列表
	 */
	private List<QuestionBean> questionList = new ArrayList<>();

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

		public int getDaTiEndTime() {
		return daTiEndTime;
	}

	public void setDaTiEndTime(int daTiEndTime) {
		this.daTiEndTime = daTiEndTime;
	}

		public List<QuestionBean> getQuestionList() {
		return questionList;
	}

	public void setQuestionList(List<QuestionBean> questionList) {
		this.questionList = questionList;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.type = readInt(buf, false);
		this.daTiEndTime = readInt(buf, false);
		int questionListLength = readShort(buf);
		for (int questionListI = 0; questionListI < questionListLength; questionListI++) {
			if (readByte(buf) == 0) { 
				this.questionList.add(null);
			} else {
				QuestionBean questionBean = new QuestionBean();
				questionBean.read(buf);
				this.questionList.add(questionBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, type, false);
		this.writeInt(buf, daTiEndTime, false);
		writeShort(buf, this.questionList.size());
		for (int questionListI = 0; questionListI < this.questionList.size(); questionListI++) {
			this.writeBean(buf, this.questionList.get(questionListI));
		}
		return true;
	}
}
