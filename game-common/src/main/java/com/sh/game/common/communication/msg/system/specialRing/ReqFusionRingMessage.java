package com.sh.game.common.communication.msg.system.specialRing;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求融合
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqFusionRingMessage extends AbsProtostuffMessage {
  /**
   * 装备唯一id
   */
  private long equipId;

  /**
   * 融合道具唯一id
   */
  private long fusionEquipId;

  /**
   * 操作背包类型 1主角 2英雄
   */
  private int type;

  /**
   * 融合类别 1特戒 2魔器
   */
  private int fusionType;

  @Override
  public int getId() {
    return 129001;
  }

  public void setEquipId(long equipId) {
    this.equipId = equipId;
  }

  public long getEquipId() {
    return this.equipId;
  }

  public void setFusionEquipId(long fusionEquipId) {
    this.fusionEquipId = fusionEquipId;
  }

  public long getFusionEquipId() {
    return this.fusionEquipId;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setFusionType(int fusionType) {
    this.fusionType = fusionType;
  }

  public int getFusionType() {
    return this.fusionType;
  }
}
