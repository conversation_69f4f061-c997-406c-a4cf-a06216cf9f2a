package com.sh.game.common.communication.msg.system.treasureHunt;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求寻宝页面信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqTreasureHuntPanelMessage extends AbsProtostuffMessage {
  /**
   * 寻宝类别 1普通寻宝 2限时寻宝
   */
  private int type;

  @Override
  public int getId() {
    return 230001;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
