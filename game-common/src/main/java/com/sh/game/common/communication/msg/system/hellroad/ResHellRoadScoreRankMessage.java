package com.sh.game.common.communication.msg.system.hellroad;

import com.sh.game.common.communication.msg.abc.bean.CommonRankingBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回地狱之路积分排行信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResHellRoadScoreRankMessage extends AbsProtostuffMessage {
  /**
   * 排行内容
   */
  private List<CommonRankingBean> bean = new ArrayList<>();

  @Override
  public int getId() {
    return 364006;
  }

  public void setBean(List<CommonRankingBean> bean) {
    this.bean = bean;
  }

  public List<CommonRankingBean> getBean() {
    return this.bean;
  }
}
