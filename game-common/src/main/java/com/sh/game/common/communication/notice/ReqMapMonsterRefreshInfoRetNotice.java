package com.sh.game.common.communication.notice;

import com.sh.game.common.communication.msg.system.monster.bean.MonsterDescBean;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 怪物详细信息消息
 *
 * <AUTHOR>
 * @date 2022/07/25 10:43
 */
@Getter
@Setter
@Notice
public class ReqMapMonsterRefreshInfoRetNotice extends ProcessNotice {

    /**
     * 角色id
     */
    long roleId;

    /**
     * 回调数据是否有效
     */
    private boolean success;

    /**
     * 地图配置id
     */
    int mapConfigId;

    /**
     * 怪物信息
     */
    List<MonsterDescBean> beanList = new ArrayList<>();
}
