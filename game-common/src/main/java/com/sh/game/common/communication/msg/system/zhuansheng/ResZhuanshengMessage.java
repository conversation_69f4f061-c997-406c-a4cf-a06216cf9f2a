package com.sh.game.common.communication.msg.system.zhuansheng;

import com.sh.game.common.communication.msg.system.zhuansheng.bean.ZhuanShengBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回转生
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResZhuanshengMessage extends AbsProtostuffMessage {
  /**
   * 转生信息
   */
  private ZhuanShengBean zhuansheng = new ZhuanShengBean();

  @Override
  public int getId() {
    return 208004;
  }

  public void setZhuansheng(ZhuanShengBean zhuansheng) {
    this.zhuansheng = zhuansheng;
  }

  public ZhuanShengBean getZhuansheng() {
    return this.zhuansheng;
  }
}
