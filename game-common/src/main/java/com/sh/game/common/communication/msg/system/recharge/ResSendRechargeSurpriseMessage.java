package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 充值惊喜消息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSendRechargeSurpriseMessage extends AbsProtostuffMessage {
  /**
   * 配置表id
   */
  private int cfgId;

  /**
   * 奖励领取状态0未领取1可领取2已领取
   */
  private int state;

  /**
   * 是否已自动弹出0已弹出1未弹出过
   */
  private int autoPopup;

  @Override
  public int getId() {
    return 39008;
  }

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getState() {
    return this.state;
  }

  public void setAutoPopup(int autoPopup) {
    this.autoPopup = autoPopup;
  }

  public int getAutoPopup() {
    return this.autoPopup;
  }
}
