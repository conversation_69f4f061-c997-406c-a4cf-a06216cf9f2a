package com.sh.game.common.communication.msg.system.suitExtend;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


import java.util.ArrayList;
import java.util.List;

/**
 * <p>返回专属套装效果继承结果</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ResSuitAttrExtendMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 183014;
	}
	
	/**
	 * 继承是否成功1：成功，0：失败
	 */
	private int sucess;
	/**
	 * 继承后的装备uid
	 */
	private long resultItemUid;
	/**
	 * 继承后的装备的套装cid
	 */
	private List<Integer> suitIdList = new ArrayList<>();

	public int getSucess() {
		return sucess;
	}

	public void setSucess(int sucess) {
		this.sucess = sucess;
	}

		public long getResultItemUid() {
		return resultItemUid;
	}

	public void setResultItemUid(long resultItemUid) {
		this.resultItemUid = resultItemUid;
	}

		public List<Integer> getSuitIdList() {
		return suitIdList;
	}

	public void setSuitIdList(List<Integer> suitIdList) {
		this.suitIdList = suitIdList;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.sucess = readInt(buf, false);
		this.resultItemUid = readLong(buf);
		int suitIdListLength = readShort(buf);
		for (int suitIdListI = 0; suitIdListI < suitIdListLength; suitIdListI++) {
			this.suitIdList.add(this.readInt(buf, false));
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, sucess, false);
		this.writeLong(buf, resultItemUid);
		writeShort(buf, this.suitIdList.size());
		for (int suitIdListI = 0; suitIdListI < this.suitIdList.size(); suitIdListI++) {
			this.writeInt(buf, this.suitIdList.get(suitIdListI), false);
		}
		return true;
	}
}
