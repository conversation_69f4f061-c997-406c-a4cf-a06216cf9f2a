package com.sh.game.common.communication.msg.system.magicweapon;

import com.sh.game.common.communication.msg.system.magicweapon.bean.MagicWeaponBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 请求法器镶嵌融合
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqMagicWeaponInlayMessage extends AbsProtostuffMessage {
  /**
   * 镶嵌信息
   */
  private List<MagicWeaponBean> weaponList = new ArrayList<>();

  @Override
  public int getId() {
    return 341004;
  }

  public void setWeaponList(List<MagicWeaponBean> weaponList) {
    this.weaponList = weaponList;
  }

  public List<MagicWeaponBean> getWeaponList() {
    return this.weaponList;
  }
}
