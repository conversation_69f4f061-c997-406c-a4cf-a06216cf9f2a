package com.sh.game.common.communication.msg.system.xuanbing;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回玄兵信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResXuanBingInfoMessage extends AbsProtostuffMessage {
  /**
   * 当前配置id
   */
  private int configId;

  @Override
  public int getId() {
    return 370002;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }
}
