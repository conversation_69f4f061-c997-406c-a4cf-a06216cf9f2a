package com.sh.game.common.communication.msg.system.jinjiezhilu;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>获取进阶之路状态信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ResJingJieInfoMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 4318;
	}
	
	/**
	 * 活动id
	 */
	private int actId;
	/**
	 * 当前活跃点数累计
	 */
	private int sum;
	/**
	 * 已消耗的活跃点数
	 */
	private int cost;
	/**
	 * 当前所在格子
	 */
	private int pos;

	public int getActId() {
		return actId;
	}

	public void setActId(int actId) {
		this.actId = actId;
	}

		public int getSum() {
		return sum;
	}

	public void setSum(int sum) {
		this.sum = sum;
	}

		public int getCost() {
		return cost;
	}

	public void setCost(int cost) {
		this.cost = cost;
	}

		public int getPos() {
		return pos;
	}

	public void setPos(int pos) {
		this.pos = pos;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.actId = readInt(buf, false);
		this.sum = readInt(buf, false);
		this.cost = readInt(buf, false);
		this.pos = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, actId, false);
		this.writeInt(buf, sum, false);
		this.writeInt(buf, cost, false);
		this.writeInt(buf, pos, false);
		return true;
	}
}
