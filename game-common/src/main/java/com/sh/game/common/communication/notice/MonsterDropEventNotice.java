package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 怪物掉落事件通知
 *
 * <AUTHOR>
 * @date 2022/07/08 17:06
 */
@Getter
@Setter
@Notice
public class MonsterDropEventNotice extends ProcessNotice {

    /**
     * 角色id
     */
    private long roleId;

    /**
     * 真正掉落的道具
     */
    private List<Item> dropList;
}
