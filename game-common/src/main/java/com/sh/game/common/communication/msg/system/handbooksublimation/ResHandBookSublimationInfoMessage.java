package com.sh.game.common.communication.msg.system.handbooksublimation;

import com.sh.game.common.communication.msg.system.handbooksublimation.bean.HandBookSublimationBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回升华信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResHandBookSublimationInfoMessage extends AbsProtostuffMessage {
  /**
   * 灵根升华组合配置id
   */
  private int suitConfigId;

  /**
   * 玩家当前灵根升华信息
   */
  private List<HandBookSublimationBean> sublimationList = new ArrayList<>();

  /**
   * 本次升华获得经验值
   */
  private int addExp;

  @Override
  public int getId() {
    return 345003;
  }

  public void setSuitConfigId(int suitConfigId) {
    this.suitConfigId = suitConfigId;
  }

  public int getSuitConfigId() {
    return this.suitConfigId;
  }

  public void setSublimationList(List<HandBookSublimationBean> sublimationList) {
    this.sublimationList = sublimationList;
  }

  public List<HandBookSublimationBean> getSublimationList() {
    return this.sublimationList;
  }

  public void setAddExp(int addExp) {
    this.addExp = addExp;
  }

  public int getAddExp() {
    return this.addExp;
  }
}
