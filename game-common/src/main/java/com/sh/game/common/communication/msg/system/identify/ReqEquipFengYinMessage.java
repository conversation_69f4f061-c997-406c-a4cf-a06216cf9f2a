package com.sh.game.common.communication.msg.system.identify;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求封印装备
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqEquipFengYinMessage extends AbsProtostuffMessage {
  /**
   * 待封印的装备
   */
  private long itemUid;

  @Override
  public int getId() {
    return 183017;
  }

  public void setItemUid(long itemUid) {
    this.itemUid = itemUid;
  }

  public long getItemUid() {
    return this.itemUid;
  }
}
