package com.sh.game.common.communication.msg.system.moneytree;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回摇钱树行会信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResUnionMoneyTreeInfoMessage extends AbsProtostuffMessage {
  /**
   * 行会id
   */
  private long unionId;

  /**
   * 摇钱树配置id
   */
  private int moneyTreeId;

  /**
   * 摇钱树升级进度
   */
  private int progress;

  @Override
  public int getId() {
    return 362004;
  }

  public void setUnionId(long unionId) {
    this.unionId = unionId;
  }

  public long getUnionId() {
    return this.unionId;
  }

  public void setMoneyTreeId(int moneyTreeId) {
    this.moneyTreeId = moneyTreeId;
  }

  public int getMoneyTreeId() {
    return this.moneyTreeId;
  }

  public void setProgress(int progress) {
    this.progress = progress;
  }

  public int getProgress() {
    return this.progress;
  }
}
