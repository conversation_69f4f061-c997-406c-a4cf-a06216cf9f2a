package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.common.entity.match.MatchPlayer;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 匹配notice
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/8/12.
 */
@Getter
@Setter
@Notice
public class MatchStartToMatcherNotice extends ProcessNotice {

    /**
     * 匹配组
     */
    private long groupId;
    /**
     * pvp类型
     */
    private int matchType;
    /**
     * 匹配组（每个组单独匹配）
     */
    private int matchGroup;

    private List<MatchPlayer> playerList = new ArrayList<>();



}
