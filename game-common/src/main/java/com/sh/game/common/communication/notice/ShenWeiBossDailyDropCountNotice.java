package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.TimeProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 更新玩家神威boss每日掉落次数
 *
 * <AUTHOR>
 * @since 2022/5/19 22:15
 */
@Getter
@Setter
@Notice
public class ShenWeiBossDailyDropCountNotice extends TimeProcessNotice {

    private long roleId;

    /**
     * 是否是流程的发起者，即第一个notice
     */
    @Override
    public boolean first() {
        return true;
    }
}
