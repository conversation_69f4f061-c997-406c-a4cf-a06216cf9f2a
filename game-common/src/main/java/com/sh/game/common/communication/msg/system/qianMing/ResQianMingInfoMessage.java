package com.sh.game.common.communication.msg.system.qianMing;

import com.sh.game.common.communication.msg.abc.bean.QianMingBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回签名系统信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResQianMingInfoMessage extends AbsProtostuffMessage {
  /**
   * 签名信息
   */
  private List<QianMingBean> beanList = new ArrayList<>();

  @Override
  public int getId() {
    return 375005;
  }

  public void setBeanList(List<QianMingBean> beanList) {
    this.beanList = beanList;
  }

  public List<QianMingBean> getBeanList() {
    return this.beanList;
  }
}
