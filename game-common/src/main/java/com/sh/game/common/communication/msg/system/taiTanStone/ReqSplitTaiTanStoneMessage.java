package com.sh.game.common.communication.msg.system.taiTanStone;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求泰坦神石拆分
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqSplitTaiTanStoneMessage extends AbsProtostuffMessage {
  /**
   * 装备唯一id
   */
  private long equipId;

  @Override
  public int getId() {
    return 347003;
  }

  public void setEquipId(long equipId) {
    this.equipId = equipId;
  }

  public long getEquipId() {
    return this.equipId;
  }
}
