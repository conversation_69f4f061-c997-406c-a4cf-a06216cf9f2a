package com.sh.game.common.communication.notice.match;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 取消匹配
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/8/12.
 */
@Getter
@Setter
@Notice
public class MatchCancelToLogicNotice extends ProcessNotice {

    /**
     * 组id
     */
    private long groupId;
    /**
     * 结果1成功，0失败
     */
    private int ret;
    /**
     * 提示
     */
    private String info;
    /**
     * 匹配类型
     */
    private int matchType;

    private List<Long> rid = new ArrayList<>();
}
