package com.sh.game.common.communication.msg.system.identify;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求资质鉴定
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqAptitudeChangeMessage extends AbsProtostuffMessage {
  /**
   * 消耗物uid
   */
  private long aptitudeItemUid;

  @Override
  public int getId() {
    return 183003;
  }

  public void setAptitudeItemUid(long aptitudeItemUid) {
    this.aptitudeItemUid = aptitudeItemUid;
  }

  public long getAptitudeItemUid() {
    return this.aptitudeItemUid;
  }
}
