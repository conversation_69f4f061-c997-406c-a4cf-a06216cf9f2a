package com.sh.game.common.communication.notice;

import com.sh.game.common.communication.msg.system.chat.ResChatMessage;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;


@Setter
@Getter
@Notice
public class MapChatNotice extends ProcessNotice {

    private long roleId;

    private int chatType;

    private ResChatMessage msg;

}
