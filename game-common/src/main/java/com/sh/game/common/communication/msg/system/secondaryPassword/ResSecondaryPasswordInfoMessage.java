package com.sh.game.common.communication.msg.system.secondaryPassword;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 二级密码信息返回
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSecondaryPasswordInfoMessage extends AbsProtostuffMessage {
  /**
   * 是否设置 1已设置 0未设置
   */
  private int install;

  /**
   * 是否锁定 1已解锁 0未解锁
   */
  private int isLock;

  /**
   * 剩余尝试次数
   */
  private int lastTimes;

  /**
   * 强制解锁时间
   */
  private int forceUnlockTime;

  /**
   * 重置次数时间
   */
  private int resetCountTime;

  /**
   * 请求类别 0登录下发 1主动请求
   */
  private int reqType;

  @Override
  public int getId() {
    return 280002;
  }

  public void setInstall(int install) {
    this.install = install;
  }

  public int getInstall() {
    return this.install;
  }

  public void setIsLock(int isLock) {
    this.isLock = isLock;
  }

  public int getIsLock() {
    return this.isLock;
  }

  public void setLastTimes(int lastTimes) {
    this.lastTimes = lastTimes;
  }

  public int getLastTimes() {
    return this.lastTimes;
  }

  public void setForceUnlockTime(int forceUnlockTime) {
    this.forceUnlockTime = forceUnlockTime;
  }

  public int getForceUnlockTime() {
    return this.forceUnlockTime;
  }

  public void setResetCountTime(int resetCountTime) {
    this.resetCountTime = resetCountTime;
  }

  public int getResetCountTime() {
    return this.resetCountTime;
  }

  public void setReqType(int reqType) {
    this.reqType = reqType;
  }

  public int getReqType() {
    return this.reqType;
  }
}
