package com.sh.game.common.communication.msg.system.magicweapon;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>法宝打造返回</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResCompoundMagicWeaponMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 31017;
	}
	
	/**
	 * item表id
	 */
	private int itemConfigId;

	public int getItemConfigId() {
		return itemConfigId;
	}

	public void setItemConfigId(int itemConfigId) {
		this.itemConfigId = itemConfigId;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.itemConfigId = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, itemConfigId, false);
		return true;
	}
}
