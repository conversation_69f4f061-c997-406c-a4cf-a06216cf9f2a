package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取限时直购奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqGetThroughRechargeRewardMessage extends AbsProtostuffMessage {
  /**
   * 配置表id
   */
  private int cfgId;

  @Override
  public int getId() {
    return 39010;
  }

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }
}
