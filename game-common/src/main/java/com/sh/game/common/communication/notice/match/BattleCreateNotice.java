package com.sh.game.common.communication.notice.match;

import com.sh.game.common.communication.msg.pvp.bean.MatchTeamBean;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/24 10:02
 */
@Getter
@Setter
@Notice
public class BattleCreateNotice extends ProcessNotice {

    private long battleId;

    private int pvpType;

    private MatchTeamBean teamA;

    private MatchTeamBean teamB;

    private List<Long> rids = new ArrayList<>();
}
