package com.sh.game.common.communication.msg.system.treasureHunt;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回寻宝页面信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResTreasureHuntPanelMessage extends AbsProtostuffMessage {
  /**
   * 寻宝类别 1普通寻宝 2限时寻宝
   */
  private int type;

  /**
   * 可用寻宝次数
   */
  private int huntCount;

  /**
   * 展示奖励组
   */
  private int rewardsShow;

  @Override
  public int getId() {
    return 230002;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setHuntCount(int huntCount) {
    this.huntCount = huntCount;
  }

  public int getHuntCount() {
    return this.huntCount;
  }

  public void setRewardsShow(int rewardsShow) {
    this.rewardsShow = rewardsShow;
  }

  public int getRewardsShow() {
    return this.rewardsShow;
  }
}
