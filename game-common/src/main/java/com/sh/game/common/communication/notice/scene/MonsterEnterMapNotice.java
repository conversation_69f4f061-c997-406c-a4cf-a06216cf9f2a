package com.sh.game.common.communication.notice.scene;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/10/18 10:23
 */
@Getter
@Setter
@Notice
public class MonsterEnterMapNotice extends ProcessNotice {

    /**
     * 怪物配置id
     */
    private int monsterId;

    /**
     * 怪物出现点x坐标
     */
    private int x;

    /**
     * 怪物出现点y坐标
     */
    private int y;

    /**
     * 数量
     */
    private int count;

    /**
     * 范围
     */
    private int range;

    /**
     * 朝向
     */
    private int dir;

    /**
     * 地图唯一id
     */
    private long mapId;

    /**
     * 死亡后是否移除
     */
    private boolean removeAfterDie;

    /**
     * 是否添加视角
     */
    private boolean addView;

    /**
     * 召唤者
     */
    private long creator;

    /**
     * 消失时间
     */
    private int overTime;
}
