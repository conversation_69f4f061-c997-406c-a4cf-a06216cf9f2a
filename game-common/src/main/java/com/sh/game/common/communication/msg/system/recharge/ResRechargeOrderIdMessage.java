package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 返回充值档位与订单号
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResRechargeOrderIdMessage extends AbsProtostuffMessage {
  /**
   * orderId
   */
  private String order_no = new String();

  /**
   * 商品id
   */
  private int product_id;

  @Override
  public int getId() {
    return 39034;
  }

  public void setOrder_no(String order_no) {
    this.order_no = order_no;
  }

  public String getOrder_no() {
    return this.order_no;
  }

  public void setProduct_id(int product_id) {
    this.product_id = product_id;
  }

  public int getProduct_id() {
    return this.product_id;
  }
}
