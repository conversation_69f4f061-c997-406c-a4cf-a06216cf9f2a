package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 沙巴克修复怪物
 */
@Getter
@Setter
@Notice
public class ShaBaKeFixMonsterNotice extends ProcessNotice {
    private int serverType;

    /**
     * 召唤弓箭手数量
     */
    private int gjNum;

    /**
     * 召唤护卫数量
     */
    private int protectNum;


    /**
     * 雇佣防守类型1主动2正常
     */
    private int protectType;
}
