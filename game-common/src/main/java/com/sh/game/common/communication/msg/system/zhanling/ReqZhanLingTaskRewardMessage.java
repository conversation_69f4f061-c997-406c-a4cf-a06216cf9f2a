package com.sh.game.common.communication.msg.system.zhanling;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取战令任务奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqZhanLingTaskRewardMessage extends AbsProtostuffMessage {
  /**
   * 战令任务表id 为0时表示一键领取
   */
  private int cid;

  @Override
  public int getId() {
    return 402005;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
