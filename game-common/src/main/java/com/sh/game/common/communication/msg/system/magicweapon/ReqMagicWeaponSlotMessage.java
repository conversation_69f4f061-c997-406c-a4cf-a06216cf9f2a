package com.sh.game.common.communication.msg.system.magicweapon;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求法宝开孔
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqMagicWeaponSlotMessage extends AbsProtostuffMessage {
  /**
   * cfg_equip_fabaokong表id
   */
  private int configId;

  @Override
  public int getId() {
    return 341001;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }
}
