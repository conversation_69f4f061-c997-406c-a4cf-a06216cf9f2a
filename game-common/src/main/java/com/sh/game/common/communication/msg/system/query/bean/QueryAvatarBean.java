package com.sh.game.common.communication.msg.system.query.bean;

import com.sh.game.common.communication.msg.abc.bean.*;
import com.sh.game.common.communication.msg.system.dunPaiShengJi.bean.SpecialUpBean;
import com.sh.game.common.communication.msg.system.lineage.bean.LineageRoleBean;
import com.sh.game.common.communication.msg.system.mix.bean.MixInfoBean;
import com.sh.game.common.communication.msg.system.ringGem.bean.RingEquipPosBean;
import com.sh.game.common.communication.msg.system.roleSkillTree.bean.SkillTreeBean;
import com.sh.game.common.communication.msg.system.shenmo.bean.ShenMoBean;
import com.sh.game.common.communication.msg.system.skill.bean.SkillBean;

import java.util.ArrayList;
import java.util.List;

public class QueryAvatarBean {
  /**
   * 玩家id
   */
  private long roleId;

  /**
   * 玩家名字
   */
  private String roleName = new String();

  /**
   * 职业
   */
  private int career;

  /**
   * 性别
   */
  private int sex;

  /**
   * 发型
   */
  private int hair;

  /**
   * 等级
   */
  private int level;

  /**
   * 封号cfgId
   */
  private int fengHaoCfgId;

  /**
   * 龙魂之力等级
   */
  private int longHunLevel;

  /**
   * 涅槃之力等级
   */
  private int niePanLevel;


  /**
   * 行会id
   */
  private long unionId;

  /**
   * 行会名字
   */
  private String unionName = new String();

  /**
   * 行会职位
   */
  private int unionPosition;

  /**
   * 装备列表
   */
  private List<CommonEquipBean> equips = new ArrayList<>();

  /**
   * 融合信息
   */
  private List<MixInfoBean> mix = new ArrayList<>();



  /**
   * 时装
   */
  private List<CommonSlotBean> fashions = new ArrayList<>();

  /**
   * 技能信息
   */
  private List<SkillBean> skillBeanList = new ArrayList<>();

  /**
   * 资质信息
   */
  private AptitudeDataBean aptitudeData = new AptitudeDataBean();

  /**
   * 觉醒阶数
   */
  private List<CommonKeyValueBean> juexingLevels = new ArrayList<>();

  /**
   * 天书等级
   */
  private int tianShuLevel;

  /**
   * 天书经验
   */
  private long tianShuExp;

  /**
   * 元婴
   */
  private List<CommonYuanYingBean> yuanYings = new ArrayList<>();

  /**
   * 江湖名望,值未cfg_ih_prestige的id
   */
  private int mingWang;

  /**
   * 玩家当前四大传承
   */
  private List<LineageRoleBean> lineageRoleBeanList = new ArrayList<>();

  /**
   * 时装等阶(items表level字段)
   */
  private int fashionLevel;

  /**
   * 注灵, key: 部位, value: 注灵配置id
   */
  private List<CommonKeyValueBean> injectSoulList = new ArrayList<>();

  /**
   * 个人称号等级
   */
  private int selfAppearLevel;

  /**
   * 神威等级
   */
  private int shenWeiLevel;

  /**
   * 兽决装备列表
   */
  private List<CommonEquipBean> shoujueEquips = new ArrayList<>();

  /**
   * 宠物外观id
   */
  private int chongWuCfgID;

  /**
   * 特戒的灵石信息
   */
  private List<RingEquipPosBean> RingStoneBeans = new ArrayList<>();

  /**
   * 已镶嵌的法宝信息
   */
  private List<MagicWeaponBlendBean> weaponList = new ArrayList<>();

  /**
   * 阵法等级(含足迹等信息)
   */
  private List<CommonKeyValueBean> magicCircleList = new ArrayList<>();

  /**
   * 盾牌升级信息
   */
  private List<SpecialUpBean> dunPaiBean = new ArrayList<>();

  /**
   * 秘籍宝石信息
   */
  private List<EquipPosGemBean> miJiStoneBeans = new ArrayList<>();

  /**
   * 宠物装备列表
   */
  private List<CommonEquipBean> chongWuEquips = new ArrayList<>();

  /**
   * 宠物装备强化信息
   */
  private List<RolePetQiangHuaBean> rolePetQiangHuaBean = new ArrayList<>();

  /**
   * 铸魂信息,key:部位 value:铸魂属性id列表
   */
  private List<CommonKeyValueListBean> zhuHunList = new ArrayList<>();

  /**
   * 签名信息
   */
  private List<QianMingBean> qianMingList = new ArrayList<>();

  /**
   * 境界强化列表,key:pos部位,value:level等级
   */
  private List<CommonKeyValueBean> realmIntensifyList = new ArrayList<>();

  /**
   * 以购买的真言礼包信息
   */
  private List<Integer> zhenYanGiftList = new ArrayList<>();

  /**
   * 玩家战力
   */
  private long fightPower;

  /**
   *  行会等级
   */
  private int unionlevel;

  /**
   *  行会成员数量
   */
  private int unionMemberCount;

  /**
   *  k 上阵类型 1 上阵 2 协同,v 伙伴配置id
   */
  private List<CommonKeyValueBean> shangZhen = new ArrayList<>();

  /**
   *  上阵神魔
   */
  private List<ShenMoBean> shenMoBean = new ArrayList<>();

  /**
   * 玩家基础属性
   */
  private List<CommonKeyValueLongBean> attribute = new ArrayList<>();

  /**
   *  玩家ip，IP归属地客户端自行查找一下
   */
  private String ip = new String();

  /**
   *  血脉套装效果，hoardType表id
   */
  private List<Integer> hoardType = new ArrayList<>();

  /**
   * 血脉系统等级，hoardlevel表id
   */
  private int levelId;

  /**
   * 血脉属性
   */
  private List<CommonKeyValueLongBean> hoard = new ArrayList<>();

  /**
   * 使用的坐骑
   */
  private int useMount;

  /**
   * 坐骑强化表配置id
   */
  private int mountQhCfgId;

  /**
   * 使用的坐骑装扮
   */
  private int useFashion;

  /**
   *  境界id
   */
  private int zhuanShengId;

  /**
   *  最大血脉品质
   */
  private int maxHoardQuality;

  /**
   * 技能树信息
   */
  private List<SkillTreeBean> skillTreeBeanList = new ArrayList<>();

  /**
   * 当前剩余技能点
   */
  private int skillTreePoint;

  public void setRoleId(long roleId) {
    this.roleId = roleId;
  }

  public long getRoleId() {
    return this.roleId;
  }

  public void setRoleName(String roleName) {
    this.roleName = roleName;
  }

  public String getRoleName() {
    return this.roleName;
  }

  public void setCareer(int career) {
    this.career = career;
  }

  public int getCareer() {
    return this.career;
  }

  public void setSex(int sex) {
    this.sex = sex;
  }

  public int getSex() {
    return this.sex;
  }

  public void setHair(int hair) {
    this.hair = hair;
  }

  public int getHair() {
    return this.hair;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getLevel() {
    return this.level;
  }

  public void setFengHaoCfgId(int fengHaoCfgId) {
    this.fengHaoCfgId = fengHaoCfgId;
  }

  public int getFengHaoCfgId() {
    return this.fengHaoCfgId;
  }

  public void setLongHunLevel(int longHunLevel) {
    this.longHunLevel = longHunLevel;
  }

  public int getLongHunLevel() {
    return this.longHunLevel;
  }

  public void setNiePanLevel(int niePanLevel) {
    this.niePanLevel = niePanLevel;
  }

  public int getNiePanLevel() {
    return this.niePanLevel;
  }

  public void setUnionId(long unionId) {
    this.unionId = unionId;
  }

  public long getUnionId() {
    return this.unionId;
  }

  public void setUnionName(String unionName) {
    this.unionName = unionName;
  }

  public String getUnionName() {
    return this.unionName;
  }

  public void setUnionPosition(int unionPosition) {
    this.unionPosition = unionPosition;
  }

  public int getUnionPosition() {
    return this.unionPosition;
  }

  public void setEquips(List<CommonEquipBean> equips) {
    this.equips = equips;
  }

  public List<CommonEquipBean> getEquips() {
    return this.equips;
  }

  public void setMix(List<MixInfoBean> mix) {
    this.mix = mix;
  }

  public List<MixInfoBean> getMix() {
    return this.mix;
  }


  public void setFashions(List<CommonSlotBean> fashions) {
    this.fashions = fashions;
  }

  public List<CommonSlotBean> getFashions() {
    return this.fashions;
  }

  public void setSkillBeanList(List<SkillBean> skillBeanList) {
    this.skillBeanList = skillBeanList;
  }

  public List<SkillBean> getSkillBeanList() {
    return this.skillBeanList;
  }

  public void setAptitudeData(AptitudeDataBean aptitudeData) {
    this.aptitudeData = aptitudeData;
  }

  public AptitudeDataBean getAptitudeData() {
    return this.aptitudeData;
  }

  public void setJuexingLevels(List<CommonKeyValueBean> juexingLevels) {
    this.juexingLevels = juexingLevels;
  }

  public List<CommonKeyValueBean> getJuexingLevels() {
    return this.juexingLevels;
  }

  public void setTianShuLevel(int tianShuLevel) {
    this.tianShuLevel = tianShuLevel;
  }

  public int getTianShuLevel() {
    return this.tianShuLevel;
  }

  public void setTianShuExp(long tianShuExp) {
    this.tianShuExp = tianShuExp;
  }

  public long getTianShuExp() {
    return this.tianShuExp;
  }

  public void setYuanYings(List<CommonYuanYingBean> yuanYings) {
    this.yuanYings = yuanYings;
  }

  public List<CommonYuanYingBean> getYuanYings() {
    return this.yuanYings;
  }

  public void setMingWang(int mingWang) {
    this.mingWang = mingWang;
  }

  public int getMingWang() {
    return this.mingWang;
  }

  public void setLineageRoleBeanList(List<LineageRoleBean> lineageRoleBeanList) {
    this.lineageRoleBeanList = lineageRoleBeanList;
  }

  public List<LineageRoleBean> getLineageRoleBeanList() {
    return this.lineageRoleBeanList;
  }

  public void setFashionLevel(int fashionLevel) {
    this.fashionLevel = fashionLevel;
  }

  public int getFashionLevel() {
    return this.fashionLevel;
  }

  public void setInjectSoulList(List<CommonKeyValueBean> injectSoulList) {
    this.injectSoulList = injectSoulList;
  }

  public List<CommonKeyValueBean> getInjectSoulList() {
    return this.injectSoulList;
  }

  public void setSelfAppearLevel(int selfAppearLevel) {
    this.selfAppearLevel = selfAppearLevel;
  }

  public int getSelfAppearLevel() {
    return this.selfAppearLevel;
  }

  public void setShenWeiLevel(int shenWeiLevel) {
    this.shenWeiLevel = shenWeiLevel;
  }

  public int getShenWeiLevel() {
    return this.shenWeiLevel;
  }

  public void setShoujueEquips(List<CommonEquipBean> shoujueEquips) {
    this.shoujueEquips = shoujueEquips;
  }

  public List<CommonEquipBean> getShoujueEquips() {
    return this.shoujueEquips;
  }

  public void setChongWuCfgID(int chongWuCfgID) {
    this.chongWuCfgID = chongWuCfgID;
  }

  public int getChongWuCfgID() {
    return this.chongWuCfgID;
  }

  public void setRingStoneBeans(List<RingEquipPosBean> RingStoneBeans) {
    this.RingStoneBeans = RingStoneBeans;
  }

  public List<RingEquipPosBean> getRingStoneBeans() {
    return this.RingStoneBeans;
  }

  public void setWeaponList(List<MagicWeaponBlendBean> weaponList) {
    this.weaponList = weaponList;
  }

  public List<MagicWeaponBlendBean> getWeaponList() {
    return this.weaponList;
  }

  public void setMagicCircleList(List<CommonKeyValueBean> magicCircleList) {
    this.magicCircleList = magicCircleList;
  }

  public List<CommonKeyValueBean> getMagicCircleList() {
    return this.magicCircleList;
  }

  public void setDunPaiBean(List<SpecialUpBean> dunPaiBean) {
    this.dunPaiBean = dunPaiBean;
  }

  public List<SpecialUpBean> getDunPaiBean() {
    return this.dunPaiBean;
  }

  public void setMiJiStoneBeans(List<EquipPosGemBean> miJiStoneBeans) {
    this.miJiStoneBeans = miJiStoneBeans;
  }

  public List<EquipPosGemBean> getMiJiStoneBeans() {
    return this.miJiStoneBeans;
  }

  public void setChongWuEquips(List<CommonEquipBean> chongWuEquips) {
    this.chongWuEquips = chongWuEquips;
  }

  public List<CommonEquipBean> getChongWuEquips() {
    return this.chongWuEquips;
  }

  public void setRolePetQiangHuaBean(List<RolePetQiangHuaBean> rolePetQiangHuaBean) {
    this.rolePetQiangHuaBean = rolePetQiangHuaBean;
  }

  public List<RolePetQiangHuaBean> getRolePetQiangHuaBean() {
    return this.rolePetQiangHuaBean;
  }

  public void setZhuHunList(List<CommonKeyValueListBean> zhuHunList) {
    this.zhuHunList = zhuHunList;
  }

  public List<CommonKeyValueListBean> getZhuHunList() {
    return this.zhuHunList;
  }

  public void setQianMingList(List<QianMingBean> qianMingList) {
    this.qianMingList = qianMingList;
  }

  public List<QianMingBean> getQianMingList() {
    return this.qianMingList;
  }

  public void setRealmIntensifyList(List<CommonKeyValueBean> realmIntensifyList) {
    this.realmIntensifyList = realmIntensifyList;
  }

  public List<CommonKeyValueBean> getRealmIntensifyList() {
    return this.realmIntensifyList;
  }

  public void setZhenYanGiftList(List<Integer> zhenYanGiftList) {
    this.zhenYanGiftList = zhenYanGiftList;
  }

  public List<Integer> getZhenYanGiftList() {
    return this.zhenYanGiftList;
  }

  public void setFightPower(long fightPower) {
    this.fightPower = fightPower;
  }

  public long getFightPower() {
    return this.fightPower;
  }

  public void setUnionlevel(int unionlevel) {
    this.unionlevel = unionlevel;
  }

  public int getUnionlevel() {
    return this.unionlevel;
  }

  public void setUnionMemberCount(int unionMemberCount) {
    this.unionMemberCount = unionMemberCount;
  }

  public int getUnionMemberCount() {
    return this.unionMemberCount;
  }

  public void setShangZhen(List<CommonKeyValueBean> shangZhen) {
    this.shangZhen = shangZhen;
  }

  public List<CommonKeyValueBean> getShangZhen() {
    return this.shangZhen;
  }

  public void setShenMoBean(List<ShenMoBean> shenMoBean) {
    this.shenMoBean = shenMoBean;
  }

  public List<ShenMoBean> getShenMoBean() {
    return this.shenMoBean;
  }

  public void setAttribute(List<CommonKeyValueLongBean> attribute) {
    this.attribute = attribute;
  }

  public List<CommonKeyValueLongBean> getAttribute() {
    return this.attribute;
  }

  public void setIp(String ip) {
    this.ip = ip;
  }

  public String getIp() {
    return this.ip;
  }

  public void setHoardType(List<Integer> hoardType) {
    this.hoardType = hoardType;
  }

  public List<Integer> getHoardType() {
    return this.hoardType;
  }

  public void setLevelId(int levelId) {
    this.levelId = levelId;
  }

  public int getLevelId() {
    return this.levelId;
  }

  public void setHoard(List<CommonKeyValueLongBean> hoard) {
    this.hoard = hoard;
  }

  public List<CommonKeyValueLongBean> getHoard() {
    return this.hoard;
  }

  public void setUseMount(int useMount) {
    this.useMount = useMount;
  }

  public int getUseMount() {
    return this.useMount;
  }

  public void setMountQhCfgId(int mountQhCfgId) {
    this.mountQhCfgId = mountQhCfgId;
  }

  public int getMountQhCfgId() {
    return this.mountQhCfgId;
  }

  public void setUseFashion(int useFashion) {
    this.useFashion = useFashion;
  }

  public int getUseFashion() {
    return this.useFashion;
  }

  public void setZhuanShengId(int zhuanShengId) {
    this.zhuanShengId = zhuanShengId;
  }

  public int getZhuanShengId() {
    return this.zhuanShengId;
  }

  public void setMaxHoardQuality(int maxHoardQuality) {
    this.maxHoardQuality = maxHoardQuality;
  }

  public int getMaxHoardQuality() {
    return this.maxHoardQuality;
  }

  public void setSkillTreeBeanList(List<SkillTreeBean> skillTreeBeanList) {
    this.skillTreeBeanList = skillTreeBeanList;
  }

  public List<SkillTreeBean> getSkillTreeBeanList() {
    return this.skillTreeBeanList;
  }

  public void setSkillTreePoint(int skillTreePoint) {
    this.skillTreePoint = skillTreePoint;
  }

  public int getSkillTreePoint() {
    return this.skillTreePoint;
  }
}
