package com.sh.game.common.communication.notice;

import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.notice.Notice;
import com.sh.game.notice.TimeProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 消耗玩家背包道具
 */
@Getter
@Setter
@Notice
public class CostItemNotice extends TimeProcessNotice {

    private byte sourceProcessorId;

    private long roleId;

    private LogAction action;

    private List<int[]> costItems;

    private boolean noCallback;

    BackpackConst.Place[] palces;

    @Override
    public boolean first() {
        return true;
    }
}
