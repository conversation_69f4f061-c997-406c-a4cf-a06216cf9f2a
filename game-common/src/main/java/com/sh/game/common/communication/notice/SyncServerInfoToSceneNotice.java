package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * SyncServerInfoToSceneNotice
 *
 * <AUTHOR>
 * @date 2020/8/28 14:00
 */
@Getter
@Setter
@Notice
public class SyncServerInfoToSceneNotice extends ProcessNotice {

    private int moduleId;

    /**
     * 开服时间
     */
    private long openTime;
    /**
     * 合服时间
     */
    private long combineTime;
}
