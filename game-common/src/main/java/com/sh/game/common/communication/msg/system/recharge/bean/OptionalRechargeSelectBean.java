package com.sh.game.common.communication.msg.system.recharge.bean;

public class OptionalRechargeSelectBean {
  /**
   * 配置表id
   */
  private int cfgId;

  /**
   * 自选1道具序号
   */
  private int itemIndex1;

  /**
   * 自选2道具序号
   */
  private int itemIndex2;

  /**
   * 奖励领取状态 0不可领取 1可领取
   */
  private int state;

  /**
   * 已购买次数
   */
  private int count;

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }

  public void setItemIndex1(int itemIndex1) {
    this.itemIndex1 = itemIndex1;
  }

  public int getItemIndex1() {
    return this.itemIndex1;
  }

  public void setItemIndex2(int itemIndex2) {
    this.itemIndex2 = itemIndex2;
  }

  public int getItemIndex2() {
    return this.itemIndex2;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getState() {
    return this.state;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
