package com.sh.game.common.communication.msg.system.magicweapon;

import com.sh.game.common.communication.msg.abc.bean.MagicWeaponBlendBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 法宝信息返回
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMagicWeaponSlotInfoMessage extends AbsProtostuffMessage {
  /**
   * 已开孔id(cfg_equip_fabaokong表id)列表
   */
  private List<Integer> configIdList = new ArrayList<>();

  /**
   * 已镶嵌的法宝信息
   */
  private List<MagicWeaponBlendBean> weaponList = new ArrayList<>();

  @Override
  public int getId() {
    return 341003;
  }

  public void setConfigIdList(List<Integer> configIdList) {
    this.configIdList = configIdList;
  }

  public List<Integer> getConfigIdList() {
    return this.configIdList;
  }

  public void setWeaponList(List<MagicWeaponBlendBean> weaponList) {
    this.weaponList = weaponList;
  }

  public List<MagicWeaponBlendBean> getWeaponList() {
    return this.weaponList;
  }
}
