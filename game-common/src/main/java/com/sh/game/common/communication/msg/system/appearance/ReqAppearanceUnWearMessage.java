package com.sh.game.common.communication.msg.system.appearance;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 *
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqAppearanceUnWearMessage extends AbsProtostuffMessage {
  private int appearanceId;

  @Override
  public int getId() {
    return 200004;
  }

  public void setAppearanceId(int appearanceId) {
    this.appearanceId = appearanceId;
  }

  public int getAppearanceId() {
    return this.appearanceId;
  }
}
