package com.sh.game.common.communication.msg.system.shenWeiTuPo;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求神威升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqShenWeiUpMessage extends AbsProtostuffMessage {
  /**
   * 升级神威的类型(1神威属性，2青龙，3白虎，4朱雀，5玄武，6麒麟，7回复技能，8防御技能)
   */
  private int type;

  @Override
  public int getId() {
    return 329002;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
