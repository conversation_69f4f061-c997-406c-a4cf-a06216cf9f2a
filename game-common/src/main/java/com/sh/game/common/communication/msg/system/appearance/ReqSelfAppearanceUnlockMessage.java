package com.sh.game.common.communication.msg.system.appearance;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求升级个人称号
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqSelfAppearanceUnlockMessage extends AbsProtostuffMessage {
  /**
   * cfg_chenghao表id
   */
  private int chenghaoId;

  /**
   * cfg_chenghao表消耗材料的下标
   */
  private int costIndex;

  @Override
  public int getId() {
    return 200021;
  }

  public void setChenghaoId(int chenghaoId) {
    this.chenghaoId = chenghaoId;
  }

  public int getChenghaoId() {
    return this.chenghaoId;
  }

  public void setCostIndex(int costIndex) {
    this.costIndex = costIndex;
  }

  public int getCostIndex() {
    return this.costIndex;
  }
}
