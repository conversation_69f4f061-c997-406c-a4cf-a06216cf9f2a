package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取返利充值奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqGetBackRechargeRewardMessage extends AbsProtostuffMessage {
  /**
   * 活动id
   */
  private int activityId;

  @Override
  public int getId() {
    return 39012;
  }

  public void setActivityId(int activityId) {
    this.activityId = activityId;
  }

  public int getActivityId() {
    return this.activityId;
  }
}
