package com.sh.game.common.communication.msg.system.qianMing;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求镶嵌签名
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqInlayQianMingMessage extends AbsProtostuffMessage {
  /**
   * 装备位
   */
  private int index;

  /**
   * 签名时装id
   */
  private int fashionId;

  @Override
  public int getId() {
    return 375002;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  public int getIndex() {
    return this.index;
  }

  public void setFashionId(int fashionId) {
    this.fashionId = fashionId;
  }

  public int getFashionId() {
    return this.fashionId;
  }
}
