package com.sh.game.common.communication.msg.system.foundation;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 基金信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResFoundationInfoMessage extends AbsProtostuffMessage {
  /**
   * 已领取基金配置id
   */
  private List<Integer> receiveIdList = new ArrayList<>();

  /**
   * 直购配置id
   */
  private List<Integer> rechargeList = new ArrayList<>();

  @Override
  public int getId() {
    return 403002;
  }

  public void setReceiveIdList(List<Integer> receiveIdList) {
    this.receiveIdList = receiveIdList;
  }

  public List<Integer> getReceiveIdList() {
    return this.receiveIdList;
  }

  public void setRechargeList(List<Integer> rechargeList) {
    this.rechargeList = rechargeList;
  }

  public List<Integer> getRechargeList() {
    return this.rechargeList;
  }
}
