package com.sh.game.common.communication.msg.system.handbook;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求解锁图鉴
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqUnlockHandbookMessage extends AbsProtostuffMessage {
  /**
   * 待解锁道具的cid
   */
  private int itemCid;

  @Override
  public int getId() {
    return 314002;
  }

  public void setItemCid(int itemCid) {
    this.itemCid = itemCid;
  }

  public int getItemCid() {
    return this.itemCid;
  }
}
