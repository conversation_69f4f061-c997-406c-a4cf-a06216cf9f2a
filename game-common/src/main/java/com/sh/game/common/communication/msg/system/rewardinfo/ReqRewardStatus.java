package com.sh.game.common.communication.msg.system.rewardinfo;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 请求查询奖励状态（批量）
 * 该文件由工具根据 rewardinfo.xml 文件自动生成，不可修改
 */
@RPC("toServer")
public class ReqRewardStatus extends AbsProtostuffMessage {
  private List<Integer> queryList = new ArrayList<>();

  @Override
  public int getId() {
    return 397003;
  }

  public void setQueryList(List<Integer> queryList) {
    this.queryList = queryList;
  }

  public List<Integer> getQueryList() {
    return this.queryList;
  }
}
