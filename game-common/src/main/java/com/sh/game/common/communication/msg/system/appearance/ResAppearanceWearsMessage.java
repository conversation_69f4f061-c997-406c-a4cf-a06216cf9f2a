package com.sh.game.common.communication.msg.system.appearance;

import com.sh.game.common.communication.msg.system.appearance.bean.AppearanceWearBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResAppearanceWearsMessage extends AbsProtostuffMessage {
  private List<AppearanceWearBean> wares = new ArrayList<>();

  @Override
  public int getId() {
    return 200012;
  }

  public void setWares(List<AppearanceWearBean> wares) {
    this.wares = wares;
  }

  public List<AppearanceWearBean> getWares() {
    return this.wares;
  }
}
