package com.sh.game.common.communication.msg.system.treasureHunt;

import com.sh.game.common.communication.msg.system.treasureHunt.bean.RingsCompoundBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回寻宝特戒可合成信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResHuntRewardThemeMessage extends AbsProtostuffMessage {
  /**
   * 寻宝特戒可合成信息
   */
  private List<RingsCompoundBean> compoundBean = new ArrayList<>();

  @Override
  public int getId() {
    return 230008;
  }

  public void setCompoundBean(List<RingsCompoundBean> compoundBean) {
    this.compoundBean = compoundBean;
  }

  public List<RingsCompoundBean> getCompoundBean() {
    return this.compoundBean;
  }
}
