package com.sh.game.common.communication.msg.system.magiccircle;

import com.sh.game.common.communication.msg.system.magiccircle.bean.MagicCircleBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回法阵信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMagicCircleInfoMessage extends AbsProtostuffMessage {
  /**
   * 法阵套装组合配置id
   */
  private int circleSuitConfigId;

  /**
   * 玩家当前法阵信息
   */
  private List<MagicCircleBean> magicCircleBeanList = new ArrayList<>();

  @Override
  public int getId() {
    return 333002;
  }

  public void setCircleSuitConfigId(int circleSuitConfigId) {
    this.circleSuitConfigId = circleSuitConfigId;
  }

  public int getCircleSuitConfigId() {
    return this.circleSuitConfigId;
  }

  public void setMagicCircleBeanList(List<MagicCircleBean> magicCircleBeanList) {
    this.magicCircleBeanList = magicCircleBeanList;
  }

  public List<MagicCircleBean> getMagicCircleBeanList() {
    return this.magicCircleBeanList;
  }
}
