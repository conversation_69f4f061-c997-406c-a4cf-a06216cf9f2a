package com.sh.game.common.communication.msg.system.taiTanStone;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回宠物信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResTaiTanStoneInfoMessage extends AbsProtostuffMessage {
  /**
   * 玩法(1附魂，2拆分)
   */
  private int playType;

  /**
   * 结果
   */
  private boolean success;

  @Override
  public int getId() {
    return 347001;
  }

  public void setPlayType(int playType) {
    this.playType = playType;
  }

  public int getPlayType() {
    return this.playType;
  }

  public void setSuccess(boolean success) {
    this.success = success;
  }

  public boolean getSuccess() {
    return this.success;
  }
}
