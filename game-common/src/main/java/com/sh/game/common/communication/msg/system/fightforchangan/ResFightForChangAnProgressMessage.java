package com.sh.game.common.communication.msg.system.fightforchangan;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 据点占领时的事件
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResFightForChangAnProgressMessage extends AbsProtostuffMessage {
  /**
   * 据点配置id
   */
  private int cfgID;

  /**
   * 服务器当前时间戳
   */
  private int nowTime;

  /**
   * 离最终占领结束时间戳
   */
  private int endTime;

  /**
   * 状态标记:  0: 据点占领取消或已中止  1: 正在占领据点 2:占领成功
   */
  private int state;

  /**
   * 当前占领者的行会id
   */
  private long belongId;

  /**
   * 当前占领者的行会名字
   */
  private String belongName = new String();

  /**
   * 当前正在占领者的行会id
   */
  private long holdId;

  /**
   * 当前正在占领者的行会名字
   */
  private String holdName = new String();

  @Override
  public int getId() {
    return 343006;
  }

  public void setCfgID(int cfgID) {
    this.cfgID = cfgID;
  }

  public int getCfgID() {
    return this.cfgID;
  }

  public void setNowTime(int nowTime) {
    this.nowTime = nowTime;
  }

  public int getNowTime() {
    return this.nowTime;
  }

  public void setEndTime(int endTime) {
    this.endTime = endTime;
  }

  public int getEndTime() {
    return this.endTime;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getState() {
    return this.state;
  }

  public void setBelongId(long belongId) {
    this.belongId = belongId;
  }

  public long getBelongId() {
    return this.belongId;
  }

  public void setBelongName(String belongName) {
    this.belongName = belongName;
  }

  public String getBelongName() {
    return this.belongName;
  }

  public void setHoldId(long holdId) {
    this.holdId = holdId;
  }

  public long getHoldId() {
    return this.holdId;
  }

  public void setHoldName(String holdName) {
    this.holdName = holdName;
  }

  public String getHoldName() {
    return this.holdName;
  }
}
