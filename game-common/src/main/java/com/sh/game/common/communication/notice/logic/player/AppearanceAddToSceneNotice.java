package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 装扮增加
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/11/8.
 */
@Notice
@Setter
@Getter
public class AppearanceAddToSceneNotice extends ProcessNotice {
    private long rid;
    //装扮id
    private int appearanceId;

    public AppearanceAddToSceneNotice() {
    }

    public AppearanceAddToSceneNotice(long rid, int appearanceId) {
        this.rid = rid;
        this.appearanceId = appearanceId;
    }
}
