package com.sh.game.common.communication.msg.system.treasureHunt;

import com.sh.game.common.communication.msg.system.treasureHunt.bean.TreasureHuntRecordBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回寻宝记录信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResTreasureHuntRecordMessage extends AbsProtostuffMessage {
  /**
   * 寻宝记录-个人
   */
  private List<TreasureHuntRecordBean> personList = new ArrayList<>();

  /**
   * 寻宝记录-全服
   */
  private List<TreasureHuntRecordBean> recordList = new ArrayList<>();

  @Override
  public int getId() {
    return 230006;
  }

  public void setPersonList(List<TreasureHuntRecordBean> personList) {
    this.personList = personList;
  }

  public List<TreasureHuntRecordBean> getPersonList() {
    return this.personList;
  }

  public void setRecordList(List<TreasureHuntRecordBean> recordList) {
    this.recordList = recordList;
  }

  public List<TreasureHuntRecordBean> getRecordList() {
    return this.recordList;
  }
}
