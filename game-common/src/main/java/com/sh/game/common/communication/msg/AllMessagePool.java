package com.sh.game.common.communication.msg;

import com.sh.game.common.msg.AutoRegisterRpcService;
import com.sh.game.server.AbstractMessagePool;

import java.util.Map;

/**
 * <p>{doc}</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class AllMessagePool extends AbstractMessagePool {

	public static AllMessagePool newInstance(Map<String, Integer> moduleConvertMap) {
		AllMessagePool allMessagePool = new AllMessagePool(moduleConvertMap);
		AutoRegisterRpcService.initRpcMessages(allMessagePool,"com.sh.game.common.communication.msg");
		allMessagePool.register();
		return allMessagePool;
	}

	private AllMessagePool(Map<String, Integer> moduleConvertMap) {
		super(moduleConvertMap);
	}

	protected void register() {
		super.register();
	}

}
