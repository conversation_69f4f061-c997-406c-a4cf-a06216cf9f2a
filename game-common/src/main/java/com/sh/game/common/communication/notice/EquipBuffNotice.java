package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Notice
public class EquipBuffNotice extends ProcessNotice {

    private long rid;


    private int buffId;

    public EquipBuffNotice() {
    }

    public EquipBuffNotice(long rid, int buffId) {
        this.rid = rid;
        this.buffId = buffId;
    }


}
