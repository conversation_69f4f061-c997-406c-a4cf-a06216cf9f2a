package com.sh.game.common.communication.msg.system.birthchart;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回命格合成信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResBirthChartCompoundMessage extends AbsProtostuffMessage {
  /**
   * 命格合成表配置id
   */
  private int configId;

  /**
   * 获取的道具配置id
   */
  private int rewardId;

  @Override
  public int getId() {
    return 380010;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setRewardId(int rewardId) {
    this.rewardId = rewardId;
  }

  public int getRewardId() {
    return this.rewardId;
  }
}
