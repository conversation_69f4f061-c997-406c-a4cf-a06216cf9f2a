package com.sh.game.common.communication.msg.system.secondaryPassword;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 操作密码返回
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSecondaryPasswordMessage extends AbsProtostuffMessage {
  /**
   * 操作类别 1设置密码 2请求解锁 3请求上锁 4修改密码 5强制解锁
   */
  private int optType;

  /**
   * 结果1成功2失败
   */
  private int result;

  /**
   * 是否设置 1已设置 0未设置
   */
  private int install;

  /**
   * 是否锁定 1已解锁 0未解锁
   */
  private int isLock;

  /**
   * 剩余尝试次数
   */
  private int lastTimes;

  /**
   * 强制解锁时间
   */
  private int forceUnlockTime;

  /**
   * 重置次数时间
   */
  private int resetCountTime;

  @Override
  public int getId() {
    return 280004;
  }

  public void setOptType(int optType) {
    this.optType = optType;
  }

  public int getOptType() {
    return this.optType;
  }

  public void setResult(int result) {
    this.result = result;
  }

  public int getResult() {
    return this.result;
  }

  public void setInstall(int install) {
    this.install = install;
  }

  public int getInstall() {
    return this.install;
  }

  public void setIsLock(int isLock) {
    this.isLock = isLock;
  }

  public int getIsLock() {
    return this.isLock;
  }

  public void setLastTimes(int lastTimes) {
    this.lastTimes = lastTimes;
  }

  public int getLastTimes() {
    return this.lastTimes;
  }

  public void setForceUnlockTime(int forceUnlockTime) {
    this.forceUnlockTime = forceUnlockTime;
  }

  public int getForceUnlockTime() {
    return this.forceUnlockTime;
  }

  public void setResetCountTime(int resetCountTime) {
    this.resetCountTime = resetCountTime;
  }

  public int getResetCountTime() {
    return this.resetCountTime;
  }
}
