package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 沙巴克服务器启动场景处理
 */
@Getter
@Setter
@Notice
public class ShaBaKeStartUpCreateNotice extends ProcessNotice {
    /**
     * 弓箭数量
     */
    private int gjNum;
    /**
     * 护卫数量
     */
    private int protectNum;

    /**
     * 城墙血量
     */
    private Map<Integer, Integer> npcHp = new HashMap<>();

    /**
     * 雇佣防守类型1主动2正常
     */
    private int protectType;

    /**
     * 上次获胜行会id
     */
    private long lastWinUnionId;

}
