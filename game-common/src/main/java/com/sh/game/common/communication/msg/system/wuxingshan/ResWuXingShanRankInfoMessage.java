package com.sh.game.common.communication.msg.system.wuxingshan;

import com.sh.game.common.communication.msg.system.wuxingshan.bean.WuXingShanRankBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回排行信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResWuXingShanRankInfoMessage extends AbsProtostuffMessage {
  /**
   * 排行
   */
  private List<WuXingShanRankBean> list = new ArrayList<>();

  @Override
  public int getId() {
    return 386002;
  }

  public void setList(List<WuXingShanRankBean> list) {
    this.list = list;
  }

  public List<WuXingShanRankBean> getList() {
    return this.list;
  }
}
