package com.sh.game.common.communication.msg.system.suitExtend;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>请求专属继承套装升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toLogic")
public class ReqSuitExtendUpgradeMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 183006;
	}
	
	/**
	 * 待升级的专属装备uid
	 */
	private long itemUid;

	public long getItemUid() {
		return itemUid;
	}

	public void setItemUid(long itemUid) {
		this.itemUid = itemUid;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.itemUid = readLong(buf);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, itemUid);
		return true;
	}
}
