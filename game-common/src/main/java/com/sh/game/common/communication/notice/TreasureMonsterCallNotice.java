package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/6/13 15:10
 */
@Setter
@Getter
@Notice
public class TreasureMonsterCallNotice extends ProcessNotice {

    private long rid;

    private int monsterId;

    private List<Item> itemAdds = new ArrayList<>();
}