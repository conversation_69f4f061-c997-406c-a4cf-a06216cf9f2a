package com.sh.game.common.communication.notice.scene;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Notice
public class ChangAnGroundItemNotice extends ProcessNotice {

    private long mapId;

    private Map<Integer, Integer> groundItems = new HashMap<>();

    private Map<Integer, Integer> groundItemCounts = new HashMap<>();

}