package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取首充奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqFirstRechargeRewardMessage extends AbsProtostuffMessage {
  /**
   * 充值id
   */
  private int rechargeId;

  @Override
  public int getId() {
    return 39003;
  }

  public void setRechargeId(int rechargeId) {
    this.rechargeId = rechargeId;
  }

  public int getRechargeId() {
    return this.rechargeId;
  }
}
