package com.sh.game.common.communication.msg.system.yuanying;

import com.sh.game.common.communication.msg.system.role.bean.AttributeBean;
import com.sh.game.common.communication.msg.system.yuanying.bean.YuanYingBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回元婴强化面板
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResYuanYingInfoMessage extends AbsProtostuffMessage {
  /**
   * 元婴总等级
   */
  private int yuanYingLevel;

  /**
   * 各部位元婴信息
   */
  private List<YuanYingBean> positionYuanYing = new ArrayList<>();

  /**
   * 总属性
   */
  private AttributeBean attribute = new AttributeBean();

  @Override
  public int getId() {
    return 311002;
  }

  public void setYuanYingLevel(int yuanYingLevel) {
    this.yuanYingLevel = yuanYingLevel;
  }

  public int getYuanYingLevel() {
    return this.yuanYingLevel;
  }

  public void setPositionYuanYing(List<YuanYingBean> positionYuanYing) {
    this.positionYuanYing = positionYuanYing;
  }

  public List<YuanYingBean> getPositionYuanYing() {
    return this.positionYuanYing;
  }

  public void setAttribute(AttributeBean attribute) {
    this.attribute = attribute;
  }

  public AttributeBean getAttribute() {
    return this.attribute;
  }
}
