package com.sh.game.common.communication.msg.system.guajisystem;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.communication.msg.system.guajisystem.bean.GuaJiLogBean;
import com.sh.game.common.communication.msg.system.guajisystem.bean.RewardBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回离线挂机信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResLiXianGuaJiMessage extends AbsProtostuffMessage {
  /**
   * 当前挂机是否已激活
   */
  private int activited;

  /**
   * 本次离线时长
   */
  private int offLineTime;

  /**
   * 挂机奖励信息
   */
  private List<RewardBean> rewardList = new ArrayList<>();

  /**
   * 任务奖励信息
   */
  private List<RewardBean> taskRewards = new ArrayList<>();

  /**
   * 挂机日志
   */
  private List<GuaJiLogBean> logs = new ArrayList<>();

  /**
   * 当前挂机收益倍数
   */
  private int rewardMultiple;

  /**
   * 挂机设置，3020 自动转生 3021 自动升级称号 3022 登陆时是否自动打开挂机窗口 3023 离线挂机,记录是否打开过 3024 无限BOSS是否开启自动领取奖励
   */
  private List<CommonKeyValueBean> setting = new ArrayList<>();

  @Override
  public int getId() {
    return 379002;
  }

  public void setActivited(int activited) {
    this.activited = activited;
  }

  public int getActivited() {
    return this.activited;
  }

  public void setOffLineTime(int offLineTime) {
    this.offLineTime = offLineTime;
  }

  public int getOffLineTime() {
    return this.offLineTime;
  }

  public void setRewardList(List<RewardBean> rewardList) {
    this.rewardList = rewardList;
  }

  public List<RewardBean> getRewardList() {
    return this.rewardList;
  }

  public void setTaskRewards(List<RewardBean> taskRewards) {
    this.taskRewards = taskRewards;
  }

  public List<RewardBean> getTaskRewards() {
    return this.taskRewards;
  }

  public void setLogs(List<GuaJiLogBean> logs) {
    this.logs = logs;
  }

  public List<GuaJiLogBean> getLogs() {
    return this.logs;
  }

  public void setRewardMultiple(int rewardMultiple) {
    this.rewardMultiple = rewardMultiple;
  }

  public int getRewardMultiple() {
    return this.rewardMultiple;
  }

  public void setSetting(List<CommonKeyValueBean> setting) {
    this.setting = setting;
  }

  public List<CommonKeyValueBean> getSetting() {
    return this.setting;
  }
}
