package com.sh.game.common.communication.msg.system.identify;

import com.sh.game.common.communication.msg.abc.bean.RandAttributeBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回资质属性
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResAptitudeAttrMessage extends AbsProtostuffMessage {
  /**
   * 资质表id
   */
  private int ziZhiCfgId;

  /**
   * 资质值
   */
  private int aptitudeValue;

  /**
   * 资质属性
   */
  private List<RandAttributeBean> attributes = new ArrayList<>();

  @Override
  public int getId() {
    return 183013;
  }

  public void setZiZhiCfgId(int ziZhiCfgId) {
    this.ziZhiCfgId = ziZhiCfgId;
  }

  public int getZiZhiCfgId() {
    return this.ziZhiCfgId;
  }

  public void setAptitudeValue(int aptitudeValue) {
    this.aptitudeValue = aptitudeValue;
  }

  public int getAptitudeValue() {
    return this.aptitudeValue;
  }

  public void setAttributes(List<RandAttributeBean> attributes) {
    this.attributes = attributes;
  }

  public List<RandAttributeBean> getAttributes() {
    return this.attributes;
  }
}
