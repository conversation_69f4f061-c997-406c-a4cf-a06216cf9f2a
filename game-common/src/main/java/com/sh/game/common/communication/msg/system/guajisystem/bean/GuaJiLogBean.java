package com.sh.game.common.communication.msg.system.guajisystem.bean;

import java.lang.String;
import java.util.ArrayList;
import java.util.List;

public class GuaJiLogBean {
  /**
   * 日志类型:  1: 重置日志  2: 杀怪日志
   */
  private int type;

  /**
   * 日志产生时间戳
   */
  private int logTime;

  /**
   * 对应cfg_lang表的id
   */
  private int logLangId;

  /**
   * 日志参数: 由前端根据此参数及logLangId格式化成最终的输出内容用于日志展示 
   */
  private List<String> logParams = new ArrayList<>();

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setLogTime(int logTime) {
    this.logTime = logTime;
  }

  public int getLogTime() {
    return this.logTime;
  }

  public void setLogLangId(int logLangId) {
    this.logLangId = logLangId;
  }

  public int getLogLangId() {
    return this.logLangId;
  }

  public void setLogParams(List<String> logParams) {
    this.logParams = logParams;
  }

  public List<String> getLogParams() {
    return this.logParams;
  }
}
