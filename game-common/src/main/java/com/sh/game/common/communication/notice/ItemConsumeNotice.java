package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 物品消耗Notice
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-10-27
 **/
@Getter
@Setter
@Notice
public class ItemConsumeNotice extends ProcessNotice {

    /**
     * roleId
     */
    private long rid;

    /**
     * 物品消耗列表
     * key: 消耗物cfgId
     * value: 物品数量
     */
    private Map<Integer, Integer> consumeLists;

}
