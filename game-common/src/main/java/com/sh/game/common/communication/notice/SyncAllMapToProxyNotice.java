package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.map.MapInfo;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * SyncMapInfoToClientNotice
 *
 * <AUTHOR>
 * @date 2020/8/28 14:27
 */
@Getter
@Setter
@Notice
public class SyncAllMapToProxyNotice extends ProcessNotice {

    private int moduleId;

    private List<MapInfo> mapList;

}
