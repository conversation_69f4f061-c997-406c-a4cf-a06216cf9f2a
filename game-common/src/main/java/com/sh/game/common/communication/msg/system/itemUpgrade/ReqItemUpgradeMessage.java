package com.sh.game.common.communication.msg.system.itemUpgrade;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求道具升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqItemUpgradeMessage extends AbsProtostuffMessage {
  /**
   * cfg_equip_forging配置id
   */
  private int cfgId;

  /**
   * 可选材料索引位（从1开始）
   */
  private int index;

  @Override
  public int getId() {
    return 328001;
  }

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  public int getIndex() {
    return this.index;
  }
}
