package com.sh.game.common.communication.msg.system.babelroad;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回通天之路信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResBabelRoadInfoMessage extends AbsProtostuffMessage {
  /**
   * 配置id
   */
  private int configId;

  /**
   * 每日奖励是否领取(0:未领取, 1:已领取)
   */
  private int receive;

  @Override
  public int getId() {
    return 336002;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setReceive(int receive) {
    this.receive = receive;
  }

  public int getReceive() {
    return this.receive;
  }
}
