package com.sh.game.common.communication.msg.system.injectsoul;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求注灵升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqInjectSoulMessage extends AbsProtostuffMessage {
  /**
   * 部位
   */
  private int pos;

  @Override
  public int getId() {
    return 327001;
  }

  public void setPos(int pos) {
    this.pos = pos;
  }

  public int getPos() {
    return this.pos;
  }
}
