package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-03-30
 **/
@Notice
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RoleSuperManUpdateNotice extends ProcessNotice {
    private long roleId;
    private boolean isSuperMan;
    private boolean isProtect;

    /**
     * 是否开启随机保护
     */
    private boolean isRandomProtect;
}
