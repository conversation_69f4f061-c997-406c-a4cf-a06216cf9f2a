package com.sh.game.common.communication.msg.system.dunPaiShengJi;

import com.sh.game.common.communication.msg.system.dunPaiShengJi.bean.SpecialUpBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回盾牌升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResDunPaiInfoMessage extends AbsProtostuffMessage {
  private List<SpecialUpBean> dunPaiBean = new ArrayList<>();

  @Override
  public int getId() {
    return 349002;
  }

  public void setDunPaiBean(List<SpecialUpBean> dunPaiBean) {
    this.dunPaiBean = dunPaiBean;
  }

  public List<SpecialUpBean> getDunPaiBean() {
    return this.dunPaiBean;
  }
}
