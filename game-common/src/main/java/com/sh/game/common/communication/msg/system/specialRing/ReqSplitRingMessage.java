package com.sh.game.common.communication.msg.system.specialRing;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求拆分
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqSplitRingMessage extends AbsProtostuffMessage {
  /**
   * 装备唯一id
   */
  private long equipId;

  /**
   * 操作背包类型 1主角 2英雄
   */
  private int type;

  /**
   * 拆分类别 1特戒 2魔器
   */
  private int splitType;

  @Override
  public int getId() {
    return 129003;
  }

  public void setEquipId(long equipId) {
    this.equipId = equipId;
  }

  public long getEquipId() {
    return this.equipId;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setSplitType(int splitType) {
    this.splitType = splitType;
  }

  public int getSplitType() {
    return this.splitType;
  }
}
