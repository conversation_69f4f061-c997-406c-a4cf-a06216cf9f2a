package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

/**
 * 怪物死亡通知
 */

@Setter
@Getter
@Notice
public class MonsterDieNotice extends ProcessNotice {

    /**
     * 所在地图
     */
    private int mapId;

    /**
     * 怪物配置编号
     */
    private int monsterId;

    /**
     * 怪物死亡点x
     */
    private int diePointX;

    /**
     * 怪物死亡点Y
     */
    private int diePointY;

    /**
     * 击杀怪物玩家编号
     */
    private long killerID;

    /**
     * 归属
     */
    private long ownerID;

    /**
     * 所有仇恨玩家编号(当前地图)
     */
    private Set<Long> threatSet;

    /**
     * 所有仇恨玩家编号(不过滤)
     */
    private Set<Long> threatAllSet;

    /**
     * 所有对当前怪物造成伤害的对象id列表
     */
    private Set<Long> hurtMap;

}
