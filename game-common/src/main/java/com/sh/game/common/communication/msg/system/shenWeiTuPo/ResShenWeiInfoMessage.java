package com.sh.game.common.communication.msg.system.shenWeiTuPo;

import com.sh.game.common.communication.msg.system.shenWeiTuPo.bean.ShenWeiBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回神威信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResShenWeiInfoMessage extends AbsProtostuffMessage {
  /**
   * 神威压制等级
   */
  private int yaZhiLevel;

  /**
   * 神威等级
   */
  private List<ShenWeiBean> beans = new ArrayList<>();

  @Override
  public int getId() {
    return 329001;
  }

  public void setYaZhiLevel(int yaZhiLevel) {
    this.yaZhiLevel = yaZhiLevel;
  }

  public int getYaZhiLevel() {
    return this.yaZhiLevel;
  }

  public void setBeans(List<ShenWeiBean> beans) {
    this.beans = beans;
  }

  public List<ShenWeiBean> getBeans() {
    return this.beans;
  }
}
