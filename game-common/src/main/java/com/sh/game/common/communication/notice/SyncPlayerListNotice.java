package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * SyncServerInfoToSceneNotice
 *
 * <AUTHOR>
 * @date 2020/8/28 14:00
 */
@Getter
@Setter
@Notice
public class SyncPlayerListNotice extends ProcessNotice {

    private int moduleId;

    /**
     * 玩家列表
     */
    private List<Long> PlayerIdList = new ArrayList<>(0);

}
