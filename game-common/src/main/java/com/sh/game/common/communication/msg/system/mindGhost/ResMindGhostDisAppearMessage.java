package com.sh.game.common.communication.msg.system.mindGhost;

import com.sh.game.common.communication.msg.abc.bean.RoleBriefBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 心魔消失
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMindGhostDisAppearMessage extends AbsProtostuffMessage {
  /**
   * 击杀者
   */
  private RoleBriefBean killer = new RoleBriefBean();

  @Override
  public int getId() {
    return 201002;
  }

  public void setKiller(RoleBriefBean killer) {
    this.killer = killer;
  }

  public RoleBriefBean getKiller() {
    return this.killer;
  }
}
