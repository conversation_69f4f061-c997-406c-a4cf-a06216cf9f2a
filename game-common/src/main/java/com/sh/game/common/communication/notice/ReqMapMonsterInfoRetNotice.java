package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/07/18 18:31
 */
@Getter
@Setter
@Notice
public class ReqMapMonsterInfoRetNotice extends ProcessNotice {

    /**
     * 角色id
     */
    long roleId;

    /**
     * 回调数据是否有效
     */
    private boolean success;

    /**
     * 地图配置id
     */
    int mapConfigId;

    /**
     * 怪物信息
     */
    Map<Integer, Integer> beanMap = new HashMap<>();
}
