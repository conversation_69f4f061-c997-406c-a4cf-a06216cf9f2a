package com.sh.game.common.communication.msg.system.moneytree;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取摇钱树升级奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqMoneyTreeRewardMessage extends AbsProtostuffMessage {
  /**
   * 摇钱树配置id
   */
  private int configId;

  /**
   * 是否额外奖励 false: 普通奖励 true: 额外奖励
   */
  private boolean extra;

  @Override
  public int getId() {
    return 362005;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setExtra(boolean extra) {
    this.extra = extra;
  }

  public boolean getExtra() {
    return this.extra;
  }
}
