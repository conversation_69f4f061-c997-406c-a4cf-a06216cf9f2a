package com.sh.game.common.communication.msg.system.rewardinfo;

import com.sh.game.common.communication.msg.system.rewardinfo.bean.RewardStatus;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回奖励状态列表（批量）
 * 该文件由工具根据 rewardinfo.xml 文件自动生成，不可修改
 */
@RPC("toClient")
public class ResRewardStatus extends AbsProtostuffMessage {
  /**
   * 奖励状态列表
   */
  private List<RewardStatus> status = new ArrayList<>();

  @Override
  public int getId() {
    return 397004;
  }

  public void setStatus(List<RewardStatus> status) {
    this.status = status;
  }

  public List<RewardStatus> getStatus() {
    return this.status;
  }
}
