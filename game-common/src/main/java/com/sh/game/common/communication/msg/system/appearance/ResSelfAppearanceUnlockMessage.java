package com.sh.game.common.communication.msg.system.appearance;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回解锁个人称号
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSelfAppearanceUnlockMessage extends AbsProtostuffMessage {
  /**
   * true成功，false失败
   */
  private boolean success;

  /**
   * 当前称号等级
   */
  private int level;

  @Override
  public int getId() {
    return 200022;
  }

  public void setSuccess(boolean success) {
    this.success = success;
  }

  public boolean getSuccess() {
    return this.success;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getLevel() {
    return this.level;
  }
}
