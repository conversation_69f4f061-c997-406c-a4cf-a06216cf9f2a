package com.sh.game.common.communication.msg.system.taiTanStone;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求泰坦神石附魂
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqTaiTanFuHunMessage extends AbsProtostuffMessage {
  /**
   * 装备唯一id
   */
  private long equipId;

  /**
   * 宝石唯一id
   */
  private long gemId;

  @Override
  public int getId() {
    return 347002;
  }

  public void setEquipId(long equipId) {
    this.equipId = equipId;
  }

  public long getEquipId() {
    return this.equipId;
  }

  public void setGemId(long gemId) {
    this.gemId = gemId;
  }

  public long getGemId() {
    return this.gemId;
  }
}
