package com.sh.game.common.communication.msg.system.strangeFire;

import com.sh.game.common.communication.msg.system.strangeFire.bean.StrangeFireBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回异火信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResStrangeFireInfoMessage extends AbsProtostuffMessage {
  /**
   * 异火信息
   */
  private List<StrangeFireBean> fireBeanList = new ArrayList<>();

  /**
   * 异火塔剩余次数
   */
  private int count;

  /**
   * 今日已购买次数
   */
  private int buyCount;

  @Override
  public int getId() {
    return 372003;
  }

  public void setFireBeanList(List<StrangeFireBean> fireBeanList) {
    this.fireBeanList = fireBeanList;
  }

  public List<StrangeFireBean> getFireBeanList() {
    return this.fireBeanList;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }

  public void setBuyCount(int buyCount) {
    this.buyCount = buyCount;
  }

  public int getBuyCount() {
    return this.buyCount;
  }
}
