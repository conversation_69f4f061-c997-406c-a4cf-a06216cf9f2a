package com.sh.game.common.communication.msg.system.zhuansheng;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求兑换转生经验
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqZhuanshengExpMessage extends AbsProtostuffMessage {
  /**
   * 配置表id
   */
  private int itemId;

  /**
   * 玩家id
   */
  private long actorId;

  @Override
  public int getId() {
    return 208005;
  }

  public void setItemId(int itemId) {
    this.itemId = itemId;
  }

  public int getItemId() {
    return this.itemId;
  }

  public void setActorId(long actorId) {
    this.actorId = actorId;
  }

  public long getActorId() {
    return this.actorId;
  }
}
