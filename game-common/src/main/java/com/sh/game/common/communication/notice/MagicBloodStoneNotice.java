package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * author: wiley
 * QQ   : 510600102
 * Date: 2021/1/19 20:10
 * desc: 魔血石通知
 */

@Getter
@Setter
@Notice
public class MagicBloodStoneNotice extends ProcessNotice {

    private long rid;

    private long actorId;

    private int pos;

    private int magicBloodStoneValue;

    public MagicBloodStoneNotice(){

    }

    public MagicBloodStoneNotice(long rid, long actorId, int pos, int magicBloodStoneValue) {
        this.rid = rid;
        this.actorId = actorId;
        this.pos = pos;
        this.magicBloodStoneValue = magicBloodStoneValue;
    }
}
