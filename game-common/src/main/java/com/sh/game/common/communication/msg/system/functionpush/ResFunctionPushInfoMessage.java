package com.sh.game.common.communication.msg.system.functionpush;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回功能预告信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResFunctionPushInfoMessage extends AbsProtostuffMessage {
  /**
   * 玩家已领取奖励id
   */
  private List<Integer> rewardId = new ArrayList<>();

  @Override
  public int getId() {
    return 320002;
  }

  public void setRewardId(List<Integer> rewardId) {
    this.rewardId = rewardId;
  }

  public List<Integer> getRewardId() {
    return this.rewardId;
  }
}
