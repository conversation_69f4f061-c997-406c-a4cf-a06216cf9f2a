package com.sh.game.common.communication.msg.system.zhuansheng;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求转生
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqZhuanshengMessage extends AbsProtostuffMessage {
  /**
   * 玩家id
   */
  private long actorId;

  @Override
  public int getId() {
    return 208003;
  }

  public void setActorId(long actorId) {
    this.actorId = actorId;
  }

  public long getActorId() {
    return this.actorId;
  }
}
