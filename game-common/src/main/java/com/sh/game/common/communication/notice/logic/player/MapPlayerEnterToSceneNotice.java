package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.common.cd.CD;
import com.sh.game.common.entity.map.RoleDTO;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 通知玩家进入场景
 */
@Notice
@Getter
@Setter
public class MapPlayerEnterToSceneNotice extends ProcessNotice {

    /**
     * 游戏服编号
     */
    private int host;

    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 地图唯一编号
     */
    private long mapId;

    /**
     * 坐标
     */
    private float x;

    /**
     * 坐标
     */
    private float y;

    /**
     * 范围
     */
    private int range;

    /**
     * 登录类型
     */
    private int enterType;

    private int deliverId;

    /**
     * 玩家数据
     */
    private RoleDTO roleDTO;

    /**
     * 队伍编号
     */
    private long teamID;

    /**
     * 当前地图需要消耗道具拥有总数
     */
    private long totalOwn;

    /**
     * 祭坛次数
     */
    private int jiTanCount;

    /**
     * cd 要传递过去
     */
    private Map<Long, CD> cdMap = new HashMap<>();

    /**
     * 是否恢复player状态
     */
    private boolean needRecover;


    /**
     * 第一次登录携带fakeId,场景删除假人
     */
    private long fakePlayerId;

    /**
     * 已激活的装扮外观
     */
    @Deprecated
    private Set<Integer> appearanceSets = new HashSet<>();

    /**
     * 继承的免费时间
     */
    @Deprecated
    private int inheritFreeTime;

    /**
     * 觉醒等级
     */
    @Deprecated
    private Map<Integer, Integer> juexingLevel = new HashMap<>();


    /**
     * 月卡
     * key: 月卡id
     * value: 到期时间(永久为-1)
     */
    @Deprecated
    private Map<Integer, Integer> monthCard = new HashMap<>();

    /**
     * 神威boss每日掉落次数
     */
    @Deprecated
    private int dailyShenWeiBossDropCount;

    /**
     * 上阵伙伴
     */
    private Set<Integer> huoban;
}
