package com.sh.game.common.communication.msg.system.appearance;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 兑换
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqAppearanceExchangeMessage extends AbsProtostuffMessage {
  private int appearanceId;

  @Override
  public int getId() {
    return 200015;
  }

  public void setAppearanceId(int appearanceId) {
    this.appearanceId = appearanceId;
  }

  public int getAppearanceId() {
    return this.appearanceId;
  }
}
