package com.sh.game.common.communication.msg.abc.bean;

import java.util.ArrayList;
import java.util.List;

public class RoleSimpleBean {
  /**
   * 角色id
   */
  private long rid;

  /**
   * 名字
   */
  private String name = new String();

  /**
   * 等级
   */
  private int level;

  /**
   * 职业
   */
  private int career;

  /**
   * 性别
   */
  private int sex;

  /**
   * 头发
   */
  private int hair;

  /**
   * 帮会名字
   */
  private String unionName = new String();

  /**
   * 装备
   */
  private List<CommonEquipBean> equips = new ArrayList<>();

  /**
   * 时装
   */
  private List<CommonSlotBean> fashions = new ArrayList<>();

  /**
   * 元婴
   */
  private List<CommonYuanYingBean> yuanYings = new ArrayList<>();

  /**
   * 注灵, key: 部位, value: 注灵配置id
   */
  private List<CommonKeyValueBean> injectSoulList = new ArrayList<>();

  /**
   * 兽决装备
   */
  private List<CommonEquipBean> shouJueEquips = new ArrayList<>();

  /**
   * 宠物id
   */
  private int chongWuCfgID;

  /**
   * 秘籍宝石信息
   */
  private List<EquipPosGemBean> miJiStoneBeans = new ArrayList<>();

  /**
   * 已镶嵌的法宝信息
   */
  private List<MagicWeaponBlendBean> weaponList = new ArrayList<>();

  /**
   * 宠物装备列表
   */
  private List<CommonEquipBean> chongWuEquips = new ArrayList<>();

  /**
   * 宠物装备强化信息
   */
  private List<RolePetQiangHuaBean> rolePetQiangHuaBean = new ArrayList<>();


  /**
   * 境界强化列表,key:pos部位,value:level等级
   */
  private List<CommonKeyValueBean> realmIntensifyList = new ArrayList<>();

  /**
   * 以购买的真言礼包信息
   */
  private List<Integer> zhenYanGiftList = new ArrayList<>();

  /**
   * 签名信息
   */
  private List<QianMingBean> qianMingList = new ArrayList<>();

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getName() {
    return this.name;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getLevel() {
    return this.level;
  }

  public void setCareer(int career) {
    this.career = career;
  }

  public int getCareer() {
    return this.career;
  }

  public void setSex(int sex) {
    this.sex = sex;
  }

  public int getSex() {
    return this.sex;
  }

  public void setHair(int hair) {
    this.hair = hair;
  }

  public int getHair() {
    return this.hair;
  }

  public void setUnionName(String unionName) {
    this.unionName = unionName;
  }

  public String getUnionName() {
    return this.unionName;
  }

  public void setEquips(List<CommonEquipBean> equips) {
    this.equips = equips;
  }

  public List<CommonEquipBean> getEquips() {
    return this.equips;
  }

  public void setFashions(List<CommonSlotBean> fashions) {
    this.fashions = fashions;
  }

  public List<CommonSlotBean> getFashions() {
    return this.fashions;
  }

  public void setYuanYings(List<CommonYuanYingBean> yuanYings) {
    this.yuanYings = yuanYings;
  }

  public List<CommonYuanYingBean> getYuanYings() {
    return this.yuanYings;
  }

  public void setInjectSoulList(List<CommonKeyValueBean> injectSoulList) {
    this.injectSoulList = injectSoulList;
  }

  public List<CommonKeyValueBean> getInjectSoulList() {
    return this.injectSoulList;
  }

  public void setShouJueEquips(List<CommonEquipBean> shouJueEquips) {
    this.shouJueEquips = shouJueEquips;
  }

  public List<CommonEquipBean> getShouJueEquips() {
    return this.shouJueEquips;
  }

  public void setChongWuCfgID(int chongWuCfgID) {
    this.chongWuCfgID = chongWuCfgID;
  }

  public int getChongWuCfgID() {
    return this.chongWuCfgID;
  }

  public void setMiJiStoneBeans(List<EquipPosGemBean> miJiStoneBeans) {
    this.miJiStoneBeans = miJiStoneBeans;
  }

  public List<EquipPosGemBean> getMiJiStoneBeans() {
    return this.miJiStoneBeans;
  }

  public void setWeaponList(List<MagicWeaponBlendBean> weaponList) {
    this.weaponList = weaponList;
  }

  public List<MagicWeaponBlendBean> getWeaponList() {
    return this.weaponList;
  }

  public void setChongWuEquips(List<CommonEquipBean> chongWuEquips) {
    this.chongWuEquips = chongWuEquips;
  }

  public List<CommonEquipBean> getChongWuEquips() {
    return this.chongWuEquips;
  }

  public void setRolePetQiangHuaBean(List<RolePetQiangHuaBean> rolePetQiangHuaBean) {
    this.rolePetQiangHuaBean = rolePetQiangHuaBean;
  }

  public List<RolePetQiangHuaBean> getRolePetQiangHuaBean() {
    return this.rolePetQiangHuaBean;
  }

  public void setRealmIntensifyList(List<CommonKeyValueBean> realmIntensifyList) {
    this.realmIntensifyList = realmIntensifyList;
  }

  public List<CommonKeyValueBean> getRealmIntensifyList() {
    return this.realmIntensifyList;
  }

  public void setZhenYanGiftList(List<Integer> zhenYanGiftList) {
    this.zhenYanGiftList = zhenYanGiftList;
  }

  public List<Integer> getZhenYanGiftList() {
    return this.zhenYanGiftList;
  }

  public void setQianMingList(List<QianMingBean> qianMingList) {
    this.qianMingList = qianMingList;
  }

  public List<QianMingBean> getQianMingList() {
    return this.qianMingList;
  }
}
