package com.sh.game.common.communication.msg.system.privilege;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 领取特权激活奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqGetPrivilegeFirstRewardMessage extends AbsProtostuffMessage {
  /**
   * 特权id
   */
  private int cid;

  @Override
  public int getId() {
    return 187005;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
