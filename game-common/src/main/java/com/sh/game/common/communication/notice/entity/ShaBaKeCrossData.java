package com.sh.game.common.communication.notice.entity;

import com.sh.game.common.entity.backpack.item.Item;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 跨服沙巴克场景数据
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-03-16
 **/
@Setter
@Getter
public class ShaBaKeCrossData {
    /**
     * key
     */
    private String key;

    /**
     * 开服后第几次
     */
    private int openTime;

    /**
     * 合服后第几次
     */
    private int mergedTime;


    /**
     * 上次获胜行会id
     */
    private long lastWinUnionId;

    /**
     * 上次获胜行会名
     */
    private String lastWinUnionName;

    /**
     * 雕像会长昵称
     */
    private String chairmanName;

    /**
     * 雕像会长性别
     */
    private int chairmanSex;

    /**
     * 开服获胜行会记录
     */
    private Map<Integer, String> openWinUnionNameMap = new ConcurrentHashMap<>();

    /**
     * 合服获胜行会记录
     */
    private Map<Integer, String> mergeWinUnionNameMap = new ConcurrentHashMap<>();

    /**
     * 城门状态
     */
    private boolean doorClose = true;

    /**
     * 雇佣防守类型1主动2正常
     */
    private int protectType;

    /**
     * 城墙血量 百分比
     */
    private Map<Integer, Integer> npcHp = new ConcurrentHashMap<>();

    /**
     * 剩余弓箭手
     */
    private int gjNum;

    /**
     * 剩余守卫
     */
    private int protectNum;

    /**
     * 城堡里得钱
     */
    private long money;


    /**
     * 是否设置过城门状态
     */
    private boolean setClose = false;


    /**
     * 沙巴克奖励
     */
    private Map<Long, Item> shaBaKeReward = new ConcurrentHashMap<>();

    /**
     * 沙巴克奖励分配情况
     */
    private Map<Long, Long> shaBaKeAlloc = new ConcurrentHashMap<>();

    /**
     * 沙巴克钻石
     */
    private int shaBaKeMoney;

    /**
     * 上次沙巴克结束时间
     */
    private int lastEndTime;

    /**
     * 沙巴克钻石是否已经发红包
     */
    private boolean moneyGet;

    /**
     * 沙巴克积分
     */
    private Map<Long, Integer> playerScores = new ConcurrentHashMap<>();

    /**
     * 沙巴克奖励是否以退还
     */
    private boolean backToLeader;

    /**
     * 会长简要信息
     */
    private RoleSimpleData chairman;

    /**
     * 副会长简要信息
     */
    private List<RoleSimpleData> viceChairman = new ArrayList<>();
}
