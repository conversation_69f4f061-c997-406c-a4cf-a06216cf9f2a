package com.sh.game.common.communication.msg.system.suitExtend;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>请求专属套装效果继承</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toLogic")
public class ReqSuitAttrExtendMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 183005;
	}
	
	/**
	 * 套装属性装备Uid
	 */
	private long sourceItemUid;
	/**
	 * 专属装备uid
	 */
	private long targetItemUid;

	public long getSourceItemUid() {
		return sourceItemUid;
	}

	public void setSourceItemUid(long sourceItemUid) {
		this.sourceItemUid = sourceItemUid;
	}

		public long getTargetItemUid() {
		return targetItemUid;
	}

	public void setTargetItemUid(long targetItemUid) {
		this.targetItemUid = targetItemUid;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.sourceItemUid = readLong(buf);
		this.targetItemUid = readLong(buf);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, sourceItemUid);
		this.writeLong(buf, targetItemUid);
		return true;
	}
}
