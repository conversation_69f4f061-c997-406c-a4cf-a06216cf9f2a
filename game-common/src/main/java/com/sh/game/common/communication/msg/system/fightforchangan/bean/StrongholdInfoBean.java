package com.sh.game.common.communication.msg.system.fightforchangan.bean;

import java.lang.String;

public class StrongholdInfoBean {
  /**
   * 据点配置id
   */
  private int cfgID;

  /**
   * 当前占领行会,  等0则未被占领
   */
  private long unionID;

  /**
   * 当前占领行会名,未占领为空
   */
  private String unionName = new String();

  /**
   * 坐标x
   */
  private int x;

  /**
   * 坐标y
   */
  private int y;

  public void setCfgID(int cfgID) {
    this.cfgID = cfgID;
  }

  public int getCfgID() {
    return this.cfgID;
  }

  public void setUnionID(long unionID) {
    this.unionID = unionID;
  }

  public long getUnionID() {
    return this.unionID;
  }

  public void setUnionName(String unionName) {
    this.unionName = unionName;
  }

  public String getUnionName() {
    return this.unionName;
  }

  public void setX(int x) {
    this.x = x;
  }

  public int getX() {
    return this.x;
  }

  public void setY(int y) {
    this.y = y;
  }

  public int getY() {
    return this.y;
  }
}
