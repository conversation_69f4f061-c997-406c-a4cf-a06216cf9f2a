package com.sh.game.common.communication.msg.system.fightforchangan;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 个人积分变化通知
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResFightForChangAnScoreChangeMessage extends AbsProtostuffMessage {
  /**
   * 此次获得新积分值
   */
  private int addScore;

  /**
   * 当前积分总值
   */
  private int totalScore;

  /**
   * 积分类型： 1：据点  2：杀人 3：被杀 4：采集
   */
  private int from;

  @Override
  public int getId() {
    return 343003;
  }

  public void setAddScore(int addScore) {
    this.addScore = addScore;
  }

  public int getAddScore() {
    return this.addScore;
  }

  public void setTotalScore(int totalScore) {
    this.totalScore = totalScore;
  }

  public int getTotalScore() {
    return this.totalScore;
  }

  public void setFrom(int from) {
    this.from = from;
  }

  public int getFrom() {
    return this.from;
  }
}
