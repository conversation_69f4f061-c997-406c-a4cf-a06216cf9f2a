package com.sh.game.common.communication.msg.system.appearance;

import com.sh.game.common.communication.msg.abc.bean.CommonItemBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 响应幸运掉落弹窗
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ResLuckDropPanelMessage extends AbsProtostuffMessage {
  /**
   * 展示道具
   */
  private List<CommonItemBean> items = new ArrayList<>();

  /**
   * 1: 幸运掉落大, 2: 幸运掉落小
   */
  private int type;

  @Override
  public int getId() {
    return 200019;
  }

  public void setItems(List<CommonItemBean> items) {
    this.items = items;
  }

  public List<CommonItemBean> getItems() {
    return this.items;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }
}
