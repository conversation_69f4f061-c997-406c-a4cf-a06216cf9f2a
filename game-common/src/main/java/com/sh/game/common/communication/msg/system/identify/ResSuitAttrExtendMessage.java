package com.sh.game.common.communication.msg.system.identify;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回专属套装效果继承结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSuitAttrExtendMessage extends AbsProtostuffMessage {
  /**
   * 继承是否成功1：成功，0：失败
   */
  private int sucess;

  /**
   * 继承后的装备uid
   */
  private long resultItemUid;

  /**
   * 升级后的装备的套装cid
   */
  private List<Integer> suitIdList = new ArrayList<>();

  @Override
  public int getId() {
    return 183014;
  }

  public void setSucess(int sucess) {
    this.sucess = sucess;
  }

  public int getSucess() {
    return this.sucess;
  }

  public void setResultItemUid(long resultItemUid) {
    this.resultItemUid = resultItemUid;
  }

  public long getResultItemUid() {
    return this.resultItemUid;
  }

  public void setSuitIdList(List<Integer> suitIdList) {
    this.suitIdList = suitIdList;
  }

  public List<Integer> getSuitIdList() {
    return this.suitIdList;
  }
}
