package com.sh.game.common.communication.msg.system.teleport;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 传送deliverId
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResTeleportDeliverIdMessage extends AbsProtostuffMessage {
  /**
   * deliver配置id
   */
  private int deliverId;

  @Override
  public int getId() {
    return 174002;
  }

  public void setDeliverId(int deliverId) {
    this.deliverId = deliverId;
  }

  public int getDeliverId() {
    return this.deliverId;
  }
}
