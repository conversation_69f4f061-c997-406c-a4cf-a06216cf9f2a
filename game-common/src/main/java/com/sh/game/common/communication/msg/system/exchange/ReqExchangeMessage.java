package com.sh.game.common.communication.msg.system.exchange;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求兑换
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqExchangeMessage extends AbsProtostuffMessage {
  /**
   * 兑换id
   */
  private int cid;

  @Override
  public int getId() {
    return 198002;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
