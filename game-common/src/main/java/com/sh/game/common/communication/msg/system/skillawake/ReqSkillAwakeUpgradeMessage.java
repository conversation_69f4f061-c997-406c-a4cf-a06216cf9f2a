package com.sh.game.common.communication.msg.system.skillawake;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求技能觉醒升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqSkillAwakeUpgradeMessage extends AbsProtostuffMessage {
  /**
   * 技能配置id
   */
  private int skillConfigId;

  @Override
  public int getId() {
    return 353003;
  }

  public void setSkillConfigId(int skillConfigId) {
    this.skillConfigId = skillConfigId;
  }

  public int getSkillConfigId() {
    return this.skillConfigId;
  }
}
