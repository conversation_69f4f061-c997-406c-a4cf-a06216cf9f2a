package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;

/**
 * 祭坛更新玩家次数
 */
@Getter
@Notice
public class JiTanRoleCountNotice extends ProcessNotice {

    private long rid;

    private int count;

    public JiTanRoleCountNotice() {
    }

    public JiTanRoleCountNotice(long rid, int count) {
        this.rid = rid;
        this.count = count;
    }
}
