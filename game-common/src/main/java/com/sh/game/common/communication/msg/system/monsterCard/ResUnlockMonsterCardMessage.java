package com.sh.game.common.communication.msg.system.monsterCard;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回解锁怪物图鉴结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResUnlockMonsterCardMessage extends AbsProtostuffMessage {
  /**
   * 解锁怪物图鉴是否成功
   */
  private boolean success;

  /**
   * 怪物图鉴表id
   */
  private int cid;

  @Override
  public int getId() {
    return 316012;
  }

  public void setSuccess(boolean success) {
    this.success = success;
  }

  public boolean getSuccess() {
    return this.success;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
