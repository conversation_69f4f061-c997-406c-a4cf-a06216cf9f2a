package com.sh.game.common.communication.msg.system.jifenstore;

import com.sh.game.common.communication.msg.system.jifenstore.bean.JiFenStoreItemBean;
import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>返回积分商店信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ResJiFenStoreInfoMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 16010;
	}
	
	/**
	 * 积分商店上次免费刷新时间
	 */
	private int jiFenStoreRefreshTime;
	/**
	 * 个人免费刷新次数
	 */
	private int freeRefreshCount;
	/**
	 * 商品信息
	 */
	private List<JiFenStoreItemBean> bean = new ArrayList<>();

	public int getJiFenStoreRefreshTime() {
		return jiFenStoreRefreshTime;
	}

	public void setJiFenStoreRefreshTime(int jiFenStoreRefreshTime) {
		this.jiFenStoreRefreshTime = jiFenStoreRefreshTime;
	}

		public int getFreeRefreshCount() {
		return freeRefreshCount;
	}

	public void setFreeRefreshCount(int freeRefreshCount) {
		this.freeRefreshCount = freeRefreshCount;
	}

		public List<JiFenStoreItemBean> getBean() {
		return bean;
	}

	public void setBean(List<JiFenStoreItemBean> bean) {
		this.bean = bean;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.jiFenStoreRefreshTime = readInt(buf, false);
		this.freeRefreshCount = readInt(buf, false);
		int beanLength = readShort(buf);
		for (int beanI = 0; beanI < beanLength; beanI++) {
			if (readByte(buf) == 0) { 
				this.bean.add(null);
			} else {
				JiFenStoreItemBean jiFenStoreItemBean = new JiFenStoreItemBean();
				jiFenStoreItemBean.read(buf);
				this.bean.add(jiFenStoreItemBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, jiFenStoreRefreshTime, false);
		this.writeInt(buf, freeRefreshCount, false);
		writeShort(buf, this.bean.size());
		for (int beanI = 0; beanI < this.bean.size(); beanI++) {
			this.writeBean(buf, this.bean.get(beanI));
		}
		return true;
	}
}
