package com.sh.game.common.communication.notice;

import com.sh.game.common.communication.msg.system.chat.bean.ChatSourceBean;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/6/30
 * @Desc : to do anything
 */
@Getter
@Setter
@Notice
public class AllianceChatReturnNotice extends ProcessNotice {


    private long unionId;

    /**
     * 聊天内容
     */
    private String content;

    /**
     * 聊天类型
     */
    private int type;

    /**
     *
     */
    private ChatSourceBean sourceBean;


    public AllianceChatReturnNotice() {
    }

    public AllianceChatReturnNotice(long unionId, String content, int type, ChatSourceBean source) {
        this.unionId = unionId;
        this.content = content;
        this.type = type;
        this.sourceBean = source;
    }
}
