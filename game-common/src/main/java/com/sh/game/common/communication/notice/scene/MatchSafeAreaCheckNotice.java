package com.sh.game.common.communication.notice.scene;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/31 9:30
 */
@Getter
@Setter
@Notice
public class MatchSafeAreaCheckNotice extends ProcessNotice {
    
    private List<Long> rids;

    /**
     * 匹配发起者id
     */
    private long matcherId;

    private int pvpType;
}
