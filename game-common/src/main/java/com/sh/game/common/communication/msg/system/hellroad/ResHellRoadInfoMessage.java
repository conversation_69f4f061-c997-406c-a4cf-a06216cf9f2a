package com.sh.game.common.communication.msg.system.hellroad;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回地狱之路信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResHellRoadInfoMessage extends AbsProtostuffMessage {
  /**
   * 积分
   */
  private int score;

  /**
   * 杀人数
   */
  private int killCount;

  /**
   * 已领取的奖励id列表
   */
  private List<Integer> rewardList = new ArrayList<>();

  @Override
  public int getId() {
    return 364003;
  }

  public void setScore(int score) {
    this.score = score;
  }

  public int getScore() {
    return this.score;
  }

  public void setKillCount(int killCount) {
    this.killCount = killCount;
  }

  public int getKillCount() {
    return this.killCount;
  }

  public void setRewardList(List<Integer> rewardList) {
    this.rewardList = rewardList;
  }

  public List<Integer> getRewardList() {
    return this.rewardList;
  }
}
