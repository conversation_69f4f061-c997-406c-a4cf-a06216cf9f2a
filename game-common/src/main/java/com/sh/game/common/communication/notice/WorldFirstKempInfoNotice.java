package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * author: wiley
 * QQ   : 510600102
 * Date: 2021/3/23 14:41
 * desc: 天下第一冠军立绘
 */
@Getter
@Setter
@Notice
public class WorldFirstKempInfoNotice extends ProcessNotice {

    /**
     * 昵称
     */
    private String name;

    /**
     * 头发
     */
    private int hair;

    /**
     * 性别
     */
    private int sex;

    /**
     * 职业
     */
    private int career;

    /**
     * 身上的装备
     */
    private Map<Integer, Integer> equips = new HashMap<>();
}
