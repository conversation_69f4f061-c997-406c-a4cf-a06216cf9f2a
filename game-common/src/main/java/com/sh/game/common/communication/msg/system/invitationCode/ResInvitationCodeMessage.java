package com.sh.game.common.communication.msg.system.invitationCode;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 返回玩家已生成的邀请码
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResInvitationCodeMessage extends AbsProtostuffMessage {
  /**
   * 是否显示邀请码
   */
  private boolean show;

  /**
   * 邀请码
   */
  private String code = new String();

  @Override
  public int getId() {
    return 330002;
  }

  public void setShow(boolean show) {
    this.show = show;
  }

  public boolean getShow() {
    return this.show;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getCode() {
    return this.code;
  }
}
