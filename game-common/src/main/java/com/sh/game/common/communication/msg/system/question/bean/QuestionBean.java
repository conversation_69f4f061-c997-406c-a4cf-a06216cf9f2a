package com.sh.game.common.communication.msg.system.question.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class QuestionBean extends KryoBean {

	/**
	 * 题目
	 */
	private String title;
	/**
	 * 答案列表
	 */
	private List<String> answerList = new ArrayList<>();
	/**
	 * 是否已答题 0未答  1已答
	 */
	private int state;

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

		public List<String> getAnswerList() {
		return answerList;
	}

	public void setAnswerList(List<String> answerList) {
		this.answerList = answerList;
	}
	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.title = readString(buf);
		int answerListLength = readShort(buf);
		for (int answerListI = 0; answerListI < answerListLength; answerListI++) {
			this.answerList.add(this.readString(buf));
		}
		this.state = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeString(buf, title);
		writeShort(buf, this.answerList.size());
		for (int answerListI = 0; answerListI < this.answerList.size(); answerListI++) {
			this.writeString(buf, this.answerList.get(answerListI));
		}
		this.writeInt(buf, state, false);
		return true;
	}
}
