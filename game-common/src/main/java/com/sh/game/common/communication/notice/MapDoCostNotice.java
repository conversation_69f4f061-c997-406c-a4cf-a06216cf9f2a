package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.TimeProcessNotice;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@Notice
public class MapDoCostNotice extends TimeProcessNotice {

    private long roleId;

    private int mapId;

    private int condition;

    @Override
    public boolean first() {
        return true;
    }

}
