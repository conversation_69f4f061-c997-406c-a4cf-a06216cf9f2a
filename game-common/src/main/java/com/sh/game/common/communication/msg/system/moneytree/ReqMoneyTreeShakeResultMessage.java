package com.sh.game.common.communication.msg.system.moneytree;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>返回摇动结果</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ReqMoneyTreeShakeResultMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 362007;
	}
	
	/**
	 * 倍率
	 */
	private int rate;

	public int getRate() {
		return rate;
	}

	public void setRate(int rate) {
		this.rate = rate;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.rate = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, rate, false);
		return true;
	}
}
