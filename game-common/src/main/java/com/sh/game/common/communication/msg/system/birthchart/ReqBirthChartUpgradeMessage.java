package com.sh.game.common.communication.msg.system.birthchart;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求命格升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqBirthChartUpgradeMessage extends AbsProtostuffMessage {
  /**
   * 装备唯一id
   */
  private long equipId;

  @Override
  public int getId() {
    return 380008;
  }

  public void setEquipId(long equipId) {
    this.equipId = equipId;
  }

  public long getEquipId() {
    return this.equipId;
  }
}
