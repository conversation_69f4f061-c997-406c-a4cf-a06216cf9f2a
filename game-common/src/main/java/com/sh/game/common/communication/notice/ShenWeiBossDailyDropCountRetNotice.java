package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 同步玩家神威boss每日掉落次数
 *
 * <AUTHOR>
 * @since 2022/5/20 16:10
 */
@Getter
@Setter
@Notice
public class ShenWeiBossDailyDropCountRetNotice extends ProcessNotice {

    private long roleId;

    /**
     * count是否有更改
     */
    private boolean success;

    /**
     * 次数
     */
    private int count;

}
