package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * ATO：yumo<br>;
 * 时间：2020/12/9 16:28<br>;
 * 版本：1.0<br>;
 * 描述：向场景中掉落多个道具
 */

@Getter
@Setter
@Notice
public class DropMultipleItemNotice extends ProcessNotice {

    /**
     * 玩家编号
     */
    private long rid;

    /**
     * 道具列表
     */
    private List<int[]> items;
}
