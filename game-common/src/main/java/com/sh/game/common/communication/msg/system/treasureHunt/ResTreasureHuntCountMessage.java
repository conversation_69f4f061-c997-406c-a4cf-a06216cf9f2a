package com.sh.game.common.communication.msg.system.treasureHunt;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回寻宝次数信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResTreasureHuntCountMessage extends AbsProtostuffMessage {
  /**
   * 寻宝类别 1普通寻宝 2限时寻宝
   */
  private int type;

  /**
   * 寻宝次数
   */
  private int count;

  @Override
  public int getId() {
    return 230010;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
