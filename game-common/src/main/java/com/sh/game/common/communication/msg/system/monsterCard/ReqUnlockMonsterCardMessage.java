package com.sh.game.common.communication.msg.system.monsterCard;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求解锁怪物图鉴
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqUnlockMonsterCardMessage extends AbsProtostuffMessage {
  /**
   * 怪物图鉴表id
   */
  private int cid;

  /**
   * 是否消耗额外材料
   */
  private boolean extraCost;

  @Override
  public int getId() {
    return 316002;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }

  public void setExtraCost(boolean extraCost) {
    this.extraCost = extraCost;
  }

  public boolean getExtraCost() {
    return this.extraCost;
  }
}
