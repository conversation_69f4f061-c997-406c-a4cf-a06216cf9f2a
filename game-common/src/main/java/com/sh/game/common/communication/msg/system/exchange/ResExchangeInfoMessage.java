package com.sh.game.common.communication.msg.system.exchange;

import com.sh.game.common.communication.msg.system.exchange.bean.ChallengeBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 剩余兑换次数信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResExchangeInfoMessage extends AbsProtostuffMessage {
  /**
   * 剩余兑换信息
   */
  private List<ChallengeBean> challengeBean = new ArrayList<>();

  @Override
  public int getId() {
    return 198001;
  }

  public void setChallengeBean(List<ChallengeBean> challengeBean) {
    this.challengeBean = challengeBean;
  }

  public List<ChallengeBean> getChallengeBean() {
    return this.challengeBean;
  }
}
