package com.sh.game.common.communication.msg.system.treasureHunt;

import com.sh.game.common.communication.msg.system.treasureHunt.bean.TreasureHuntRewardBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回寻宝结果信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResTreasureHuntMessage extends AbsProtostuffMessage {
  /**
   * 寻宝类别 1普通寻宝 2限时寻宝
   */
  private int type;

  /**
   * 寻宝次数 1单次 10十次
   */
  private int count;

  /**
   * 可用寻宝次数
   */
  private int huntCount;

  /**
   * 寻宝获得道具
   */
  private List<TreasureHuntRewardBean> items = new ArrayList<>();

  /**
   * 寻宝额外获得道具
   */
  private List<TreasureHuntRewardBean> exItems = new ArrayList<>();

  @Override
  public int getId() {
    return 230004;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }

  public void setHuntCount(int huntCount) {
    this.huntCount = huntCount;
  }

  public int getHuntCount() {
    return this.huntCount;
  }

  public void setItems(List<TreasureHuntRewardBean> items) {
    this.items = items;
  }

  public List<TreasureHuntRewardBean> getItems() {
    return this.items;
  }

  public void setExItems(List<TreasureHuntRewardBean> exItems) {
    this.exItems = exItems;
  }

  public List<TreasureHuntRewardBean> getExItems() {
    return this.exItems;
  }
}
