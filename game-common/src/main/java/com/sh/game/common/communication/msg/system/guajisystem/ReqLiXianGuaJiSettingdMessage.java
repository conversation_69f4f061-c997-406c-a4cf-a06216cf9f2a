package com.sh.game.common.communication.msg.system.guajisystem;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 挂机设置，3020 自动转生 3021 自动升级称号 3022 登陆时是否自动打开挂机窗口 3023 离线挂机,记录是否打开过 3024 无限BOSS是否开启自动领取奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqLiXianGuaJiSettingdMessage extends AbsProtostuffMessage {
  /**
   * 3020 自动转生 3021 自动升级称号 3022 登陆时是否自动打开挂机窗口 3023 离线挂机,记录是否打开过 3024 无限BOSS是否开启自动领取奖励
   */
  private int index;

  /**
   * 值 1表示开启 其他关闭
   */
  private int value;

  @Override
  public int getId() {
    return 379006;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  public int getIndex() {
    return this.index;
  }

  public void setValue(int value) {
    this.value = value;
  }

  public int getValue() {
    return this.value;
  }
}
