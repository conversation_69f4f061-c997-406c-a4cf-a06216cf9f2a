package com.sh.game.common.communication.msg.system.hellroad;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取地狱之路击杀人数奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqHellRoadKillRewardMessage extends AbsProtostuffMessage {
  /**
   * 地狱之路配置id
   */
  private int cfgId;

  @Override
  public int getId() {
    return 364001;
  }

  public void setCfgId(int cfgId) {
    this.cfgId = cfgId;
  }

  public int getCfgId() {
    return this.cfgId;
  }
}
