package com.sh.game.common.communication.msg.system.equipfirstgain;

import com.sh.game.common.communication.msg.system.equipfirstgain.bean.PersonalEquipFirstGainBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回个人装备首爆信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResPersonalEquipFirstGainInfoMessage extends AbsProtostuffMessage {
  /**
   * 个人装备首爆信息
   */
  private List<PersonalEquipFirstGainBean> personalEquipFirstGainBeanList = new ArrayList<>();

  @Override
  public int getId() {
    return 326003;
  }

  public void setPersonalEquipFirstGainBeanList(
      List<PersonalEquipFirstGainBean> personalEquipFirstGainBeanList) {
    this.personalEquipFirstGainBeanList = personalEquipFirstGainBeanList;
  }

  public List<PersonalEquipFirstGainBean> getPersonalEquipFirstGainBeanList() {
    return this.personalEquipFirstGainBeanList;
  }
}
