package com.sh.game.common.communication.msg.system.roleSkillTree;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求降级技能树技能
 * 该文件由工具根据 roleSkillTree.xml 文件自动生成，不可修改
 */
@RPC("toServer")
public class ReqRoleSkillTreeLevelDownMessage extends AbsProtostuffMessage {
  /**
   * 技能树ID
   */
  private int cid;

  @Override
  public int getId() {
    return 415006;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
