package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@Notice
public class MapDropItemNotice extends ProcessNotice {
    private long discarder;

    private long owner;

    private long mapId;

    private int x;

    private int y;

    private List<Item> items;

}
