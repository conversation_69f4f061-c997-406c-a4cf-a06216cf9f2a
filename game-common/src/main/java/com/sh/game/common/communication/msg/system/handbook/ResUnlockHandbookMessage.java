package com.sh.game.common.communication.msg.system.handbook;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回解锁图鉴结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResUnlockHandbookMessage extends AbsProtostuffMessage {
  /**
   * 解锁装备的cid
   */
  private int equipCid;

  @Override
  public int getId() {
    return 314012;
  }

  public void setEquipCid(int equipCid) {
    this.equipCid = equipCid;
  }

  public int getEquipCid() {
    return this.equipCid;
  }
}
