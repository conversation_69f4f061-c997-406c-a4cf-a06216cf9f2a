package com.sh.game.common.communication.notice;

import com.sh.game.common.communication.notice.entity.XiuLuoInfo;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/13 18:39
 */
@Notice
@Getter
@Setter
public class XiuLuoEndNotice extends ProcessNotice {


    private Map<Long, List<XiuLuoInfo>> teamInfo = new HashMap<>(2);

    private List<Long> offLine = new ArrayList<>(6);

    private long winTeam;

    private int time;

    /**
     * 本次参加的玩家id
     */
    private List<Long> rids = new ArrayList<>(6);
}
