package com.sh.game.common.communication.msg.system.official;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求兑换荣誉值
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqConvertHonorMessage extends AbsProtostuffMessage {
  /**
   * 道具id
   */
  private int itemId;

  /**
   * 道具数量
   */
  private int itemSum;

  @Override
  public int getId() {
    return 209001;
  }

  public void setItemId(int itemId) {
    this.itemId = itemId;
  }

  public int getItemId() {
    return this.itemId;
  }

  public void setItemSum(int itemSum) {
    this.itemSum = itemSum;
  }

  public int getItemSum() {
    return this.itemSum;
  }
}
