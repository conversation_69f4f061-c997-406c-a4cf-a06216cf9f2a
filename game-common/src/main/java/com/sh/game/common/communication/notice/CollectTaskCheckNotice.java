package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/1/31
 * @Desc : to do anything
 */
@Getter
@Setter
@Notice
public class CollectTaskCheckNotice extends ProcessNotice {

    private long rid;

    private long lid;

    private int mid;
}
