package com.sh.game.common.communication.notice;

import com.sh.game.common.communication.msg.system.chat.ResChatMessage;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Notice
public class RemoteChatNotice extends ProcessNotice {

    private ResChatMessage msg;


    /**
     * 阵营类型
     * 0：非阵营聊天
     */
    private int campType;
}
