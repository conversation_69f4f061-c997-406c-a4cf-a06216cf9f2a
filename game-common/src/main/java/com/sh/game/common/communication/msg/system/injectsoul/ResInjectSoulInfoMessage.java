package com.sh.game.common.communication.msg.system.injectsoul;

import com.sh.game.common.communication.msg.system.injectsoul.bean.InjectSoulBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回注灵信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResInjectSoulInfoMessage extends AbsProtostuffMessage {
  /**
   * 注灵信息
   */
  private List<InjectSoulBean> InjectSoulBeanList = new ArrayList<>();

  @Override
  public int getId() {
    return 327003;
  }

  public void setInjectSoulBeanList(List<InjectSoulBean> InjectSoulBeanList) {
    this.InjectSoulBeanList = InjectSoulBeanList;
  }

  public List<InjectSoulBean> getInjectSoulBeanList() {
    return this.InjectSoulBeanList;
  }
}
