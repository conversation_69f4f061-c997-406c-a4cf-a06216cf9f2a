package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回双倍灵符信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResDoubleLingFuInfoMessage extends AbsProtostuffMessage {
  /**
   * 充值表id
   */
  private List<Integer> cfgId = new ArrayList<>();

  @Override
  public int getId() {
    return 39036;
  }

  public void setCfgId(List<Integer> cfgId) {
    this.cfgId = cfgId;
  }

  public List<Integer> getCfgId() {
    return this.cfgId;
  }
}
