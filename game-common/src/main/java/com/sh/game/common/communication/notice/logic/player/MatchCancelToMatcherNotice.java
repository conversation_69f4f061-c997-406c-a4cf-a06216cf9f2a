package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 取消匹配
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/8/12.
 */
@Getter
@Setter
@Notice
public class MatchCancelToMatcherNotice extends ProcessNotice {

    /**
     * 组id
     */
    private long groupId;
    /**
     * 匹配类型
     */
    private int matchType;



}
