package com.sh.game.common.communication.msg.system.clientdata;

import com.sh.game.common.communication.msg.system.clientdata.bean.ClientDataBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回客户端存储数据
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResClientDataMessage extends AbsProtostuffMessage {
  private List<ClientDataBean> clientDataBean = new ArrayList<>();

  @Override
  public int getId() {
    return 391004;
  }

  public void setClientDataBean(List<ClientDataBean> clientDataBean) {
    this.clientDataBean = clientDataBean;
  }

  public List<ClientDataBean> getClientDataBean() {
    return this.clientDataBean;
  }
}
