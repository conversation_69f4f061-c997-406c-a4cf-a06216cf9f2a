package com.sh.game.common.communication.msg.system.secondaryPassword;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求二级密码信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqSecondaryPasswordInfoMessage extends AbsProtostuffMessage {
  /**
   * 请求类别 0登录下发 1主动请求
   */
  private int reqType;

  @Override
  public int getId() {
    return 280001;
  }

  public void setReqType(int reqType) {
    this.reqType = reqType;
  }

  public int getReqType() {
    return this.reqType;
  }
}
