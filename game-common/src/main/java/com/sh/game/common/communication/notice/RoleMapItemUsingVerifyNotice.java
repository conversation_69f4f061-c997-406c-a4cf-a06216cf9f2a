package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 玩家在地图中使用道具
 */

@Getter
@Setter
@Notice
public class RoleMapItemUsingVerifyNotice extends ProcessNotice {

    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 道具唯一编号
     */
    private long itemId;

    /**
     * 道具配置编号
     */
    private int itemCfgId;

    /**
     * 使用数量
     */
    private int itemCount;

    /**
     * 道具使用参数
     */
    private List<Integer> params;


}
