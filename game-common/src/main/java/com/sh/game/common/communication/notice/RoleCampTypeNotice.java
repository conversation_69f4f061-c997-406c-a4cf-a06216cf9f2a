package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2022-05-09
 **/
@Notice
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RoleCampTypeNotice extends ProcessNotice {
    private long roleId;
    private int campType;
}
