package com.sh.game.common.communication.msg.system.official;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回官职升阶信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResUpGradeMessage extends AbsProtostuffMessage {
  /**
   * 升级后的官职id
   */
  private int officialId;

  @Override
  public int getId() {
    return 209006;
  }

  public void setOfficialId(int officialId) {
    this.officialId = officialId;
  }

  public int getOfficialId() {
    return this.officialId;
  }
}
