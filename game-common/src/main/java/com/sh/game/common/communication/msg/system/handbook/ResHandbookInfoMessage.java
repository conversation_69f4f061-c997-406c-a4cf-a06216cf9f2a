package com.sh.game.common.communication.msg.system.handbook;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回专属图鉴信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResHandbookInfoMessage extends AbsProtostuffMessage {
  /**
   * 已收集的专属图鉴表id
   */
  private List<Integer> handBookCidList = new ArrayList<>();

  /**
   * 已领取的专属图鉴进度表id
   */
  private List<Integer> goalsCidList = new ArrayList<>();

  @Override
  public int getId() {
    return 314011;
  }

  public void setHandBookCidList(List<Integer> handBookCidList) {
    this.handBookCidList = handBookCidList;
  }

  public List<Integer> getHandBookCidList() {
    return this.handBookCidList;
  }

  public void setGoalsCidList(List<Integer> goalsCidList) {
    this.goalsCidList = goalsCidList;
  }

  public List<Integer> getGoalsCidList() {
    return this.goalsCidList;
  }
}
