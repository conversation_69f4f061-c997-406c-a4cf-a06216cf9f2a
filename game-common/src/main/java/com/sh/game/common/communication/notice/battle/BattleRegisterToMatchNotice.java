package com.sh.game.common.communication.notice.battle;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/8/12.
 */
@Notice
@Setter
@Getter
public class BattleRegisterToMatchNotice extends ProcessNotice {
    /**
     * 注册秘钥
     */
    private String registerKey;
    /**
     * 主机
     */
    private int hostId;
    /**
     * 服务器id
     */
    private String ip;
    /**
     * 端口
     */
    private int port;
    /**
     * 服务器类型1.游戏服，2.战斗服
     */
    private int serverType;
    /**
     * 支持的pvp类型
     */
    private List<Integer> pvpTypeList = new ArrayList<>();

    public String getRegisterKey() {
        return registerKey;
    }

    public void setRegisterKey(String registerKey) {
        this.registerKey = registerKey;
    }

    public int getHostId() {
        return hostId;
    }

    public void setHostId(int hostId) {
        this.hostId = hostId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public int getServerType() {
        return serverType;
    }

    public void setServerType(int serverType) {
        this.serverType = serverType;
    }

    public List<Integer> getPvpTypeList() {
        return pvpTypeList;
    }

    public void setPvpTypeList(List<Integer> pvpTypeList) {
        this.pvpTypeList = pvpTypeList;
    }
}
