package com.sh.game.common.communication.msg.system.babelroad;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回挑战结果信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResDuplicateResultMessage extends AbsProtostuffMessage {
  /**
   * 配置id
   */
  private int configId;

  /**
   * 挑战结果(0:失败, 1:成功)
   */
  private int success;

  @Override
  public int getId() {
    return 336005;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setSuccess(int success) {
    this.success = success;
  }

  public int getSuccess() {
    return this.success;
  }
}
