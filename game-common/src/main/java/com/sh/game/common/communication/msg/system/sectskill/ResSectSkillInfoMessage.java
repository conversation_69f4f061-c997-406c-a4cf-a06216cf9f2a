package com.sh.game.common.communication.msg.system.sectskill;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回门派技能信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSectSkillInfoMessage extends AbsProtostuffMessage {
  /**
   * 门派技能id列表
   */
  private List<Integer> configIdList = new ArrayList<>();

  @Override
  public int getId() {
    return 339002;
  }

  public void setConfigIdList(List<Integer> configIdList) {
    this.configIdList = configIdList;
  }

  public List<Integer> getConfigIdList() {
    return this.configIdList;
  }
}
