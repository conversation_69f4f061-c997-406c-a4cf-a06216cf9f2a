package com.sh.game.common.communication.msg.system.specialRing;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回操作结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSpecialRingMessage extends AbsProtostuffMessage {
  /**
   * 操作类型 1融合 2拆分
   */
  private int optType;

  /**
   * 1特戒 2魔器
   */
  private int resultType;

  /**
   * 操作结果 1成功 0失败
   */
  private int result;

  @Override
  public int getId() {
    return 129002;
  }

  public void setOptType(int optType) {
    this.optType = optType;
  }

  public int getOptType() {
    return this.optType;
  }

  public void setResultType(int resultType) {
    this.resultType = resultType;
  }

  public int getResultType() {
    return this.resultType;
  }

  public void setResult(int result) {
    this.result = result;
  }

  public int getResult() {
    return this.result;
  }
}
