package com.sh.game.common.communication.msg.system.zhanling;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 领取额外奖励状态
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResExtraRewardInfoMessage extends AbsProtostuffMessage {
  /**
   * 活动ID
   */
  private int actId;

  /**
   * 领取状态，true:已领取 false:未领取
   */
  private boolean receive;

  /**
   * 累积购买次数
   */
  private int count;

  @Override
  public int getId() {
    return 402002;
  }

  public void setActId(int actId) {
    this.actId = actId;
  }

  public int getActId() {
    return this.actId;
  }

  public void setReceive(boolean receive) {
    this.receive = receive;
  }

  public boolean getReceive() {
    return this.receive;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
