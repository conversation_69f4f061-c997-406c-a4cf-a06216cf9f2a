package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import com.sh.game.remote.rpc.RPCConnection;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 同步地图
 *
 * <AUTHOR>
 * @date 2022/12/07 11:18
 */
@Getter
@Setter
@Notice
public class SceneSyncNotice extends ProcessNotice {

}
