package com.sh.game.common.communication.notice;

import com.sh.game.common.config.model.BuffConfig;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@Notice
public class MonsterDropFilterResNotice extends ProcessNotice {

    private long rid;

    private int monsterCfgId;

    private int dropType;

    private long mapKey;

    private int x;

    private int y;

    private List<Item> dropList;
    private List<BuffConfig> dropBuffList;

    private int hung;
}
