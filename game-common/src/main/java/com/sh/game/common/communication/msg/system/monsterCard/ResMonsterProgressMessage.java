package com.sh.game.common.communication.msg.system.monsterCard;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回怪物数量信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMonsterProgressMessage extends AbsProtostuffMessage {
  /**
   * 怪物图鉴进度  key: monsterCfgid value: 数量
   */
  private CommonKeyValueBean bean = new CommonKeyValueBean();

  @Override
  public int getId() {
    return 316014;
  }

  public void setBean(CommonKeyValueBean bean) {
    this.bean = bean;
  }

  public CommonKeyValueBean getBean() {
    return this.bean;
  }
}
