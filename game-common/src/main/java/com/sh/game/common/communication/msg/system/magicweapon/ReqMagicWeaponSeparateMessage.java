package com.sh.game.common.communication.msg.system.magicweapon;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 请求法器分离
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqMagicWeaponSeparateMessage extends AbsProtostuffMessage {
  /**
   * 孔位配置id列表
   */
  private List<Integer> slotConfigIdList = new ArrayList<>();

  @Override
  public int getId() {
    return 341005;
  }

  public void setSlotConfigIdList(List<Integer> slotConfigIdList) {
    this.slotConfigIdList = slotConfigIdList;
  }

  public List<Integer> getSlotConfigIdList() {
    return this.slotConfigIdList;
  }
}
