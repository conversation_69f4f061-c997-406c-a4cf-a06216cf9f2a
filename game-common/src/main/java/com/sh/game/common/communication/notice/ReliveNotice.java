package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 请求复活
 *
 * <AUTHOR>
 * @date 2022/6/9 15:21
 */
@Setter
@Getter
@Notice
public class ReliveNotice extends ProcessNotice {

    /**
     * 角色id
     */
    private long roleId;

    /**
     * 复活后血量百分比
     */
    private int relivePercent;
}
