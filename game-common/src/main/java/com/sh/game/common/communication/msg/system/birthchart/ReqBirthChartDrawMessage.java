package com.sh.game.common.communication.msg.system.birthchart;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求猎命
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqBirthChartDrawMessage extends AbsProtostuffMessage {
  /**
   * 事件id
   */
  private int configId;

  @Override
  public int getId() {
    return 380003;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }
}
