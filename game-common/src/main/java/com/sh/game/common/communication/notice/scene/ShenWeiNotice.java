package com.sh.game.common.communication.notice.scene;

import com.sh.game.common.config.model.ShenWeiConfig;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description:
 * @Date 2022-05-07 13:52
 **/

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Notice
public class ShenWeiNotice  extends ProcessNotice {

    private long roleId;

    /**
     * 神威type {@link ShenWeiConfig#getType()}
     * value:   {@link ShenWeiConfig#getId()}
     */
    private Map<Integer, Integer> shenWei = new HashMap<>();
}
