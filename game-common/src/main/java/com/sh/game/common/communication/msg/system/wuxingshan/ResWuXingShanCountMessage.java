package com.sh.game.common.communication.msg.system.wuxingshan;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回挑战每日次数
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResWuXingShanCountMessage extends AbsProtostuffMessage {
  /**
   *  次数
   */
  private int count;

  @Override
  public int getId() {
    return 386006;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCount() {
    return this.count;
  }
}
