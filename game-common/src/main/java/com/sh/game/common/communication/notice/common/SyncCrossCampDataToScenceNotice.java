package com.sh.game.common.communication.notice.common;

import com.sh.game.common.unionCamp.CrossUnionCamp;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-05-09
 **/
@Getter
@Setter
@Notice
public class SyncCrossCampDataToScenceNotice extends ProcessNotice {
    private List<CrossUnionCamp> serverCampData = new CopyOnWriteArrayList<>();
}
