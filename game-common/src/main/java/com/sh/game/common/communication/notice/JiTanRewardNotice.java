package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

/**
 * 祭坛次数处理
 */
@Getter
@Setter
@Notice
public class JiTanRewardNotice extends ProcessNotice {

    /**
     * 助攻玩家id
     */
    private Set<Long> rids = new HashSet<>();

    /**
     * 玩家次数类型1击杀2助攻
     */
    private int countType;

    /**
     * 击杀玩家
     */
    private long killRid;
}
