package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 月卡购买
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-03-25
 **/
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Notice
public class MonthCardUptateNotice extends ProcessNotice {
    private long roleId;

    /**
     * key:     月卡特权id
     * value:   到期时间(-1为永久)
     */
    private Map<Integer, Integer> monthCard = new HashMap<>();
}
