package com.sh.game.common.communication.msg.system.privilege;

import com.sh.game.common.communication.msg.system.privilege.bean.PrivilegeBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 玩家特权信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResPrivilegeInfoMessage extends AbsProtostuffMessage {
  /**
   * 特权信息
   */
  private List<PrivilegeBean> beanList = new ArrayList<>();

  @Override
  public int getId() {
    return 187002;
  }

  public void setBeanList(List<PrivilegeBean> beanList) {
    this.beanList = beanList;
  }

  public List<PrivilegeBean> getBeanList() {
    return this.beanList;
  }
}
