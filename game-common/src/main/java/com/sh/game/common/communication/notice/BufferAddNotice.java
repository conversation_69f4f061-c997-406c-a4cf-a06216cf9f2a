package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/7/18
 * @Desc : to do anything
 */
@Getter
@Setter
@Notice
public class BufferAddNotice extends ProcessNotice {

    private long rid;

    private int bufferId;

    private List<Integer> monsterCfgId;

}
