package com.sh.game.common.communication.msg.system.material;

import com.sh.game.common.communication.msg.system.material.bean.MaterialCountBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 材料副本副本面板信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResOpenMaterialInfoMessage extends AbsProtostuffMessage {
  /**
   * 材料副本信息
   */
  private List<MaterialCountBean> materialCountBeans = new ArrayList<>();

  @Override
  public int getId() {
    return 179005;
  }

  public void setMaterialCountBeans(List<MaterialCountBean> materialCountBeans) {
    this.materialCountBeans = materialCountBeans;
  }

  public List<MaterialCountBean> getMaterialCountBeans() {
    return this.materialCountBeans;
  }
}
