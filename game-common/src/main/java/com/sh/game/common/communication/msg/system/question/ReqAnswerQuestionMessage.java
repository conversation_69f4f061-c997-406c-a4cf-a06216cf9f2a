package com.sh.game.common.communication.msg.system.question;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>请求答题</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class ReqAnswerQuestionMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 64002;
	}
	
	/**
	 * 触发组
	 */
	private int type;
	/**
	 * 题目编号 从0开始
	 */
	private int index;
	/**
	 * 答案
	 */
	private String answer;

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

		public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

		public String getAnswer() {
		return answer;
	}

	public void setAnswer(String answer) {
		this.answer = answer;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.type = readInt(buf, false);
		this.index = readInt(buf, false);
		this.answer = readString(buf);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, type, false);
		this.writeInt(buf, index, false);
		this.writeString(buf, answer);
		return true;
	}
}
