package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.communication.msg.system.recharge.bean.FistRewardInfo;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 发送首充奖励信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResFirstRechargeInfoMessage extends AbsProtostuffMessage {
  /**
   * 每日充值信息
   */
  private List<FistRewardInfo> rewardInfos = new ArrayList<>();

  @Override
  public int getId() {
    return 39004;
  }

  public void setRewardInfos(List<FistRewardInfo> rewardInfos) {
    this.rewardInfos = rewardInfos;
  }

  public List<FistRewardInfo> getRewardInfos() {
    return this.rewardInfos;
  }
}
