package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;


@Notice
@Getter
@Setter
public class RobotTeleportNotice extends ProcessNotice {

    private int cfgId;

    private int mapCfgId;

    private long mapId;

    private int x;

    private int y;


    private String name;

    private int action;

    private int level;

    private Map<Integer, Integer> equips;

    private List<int[]> skills;

}
