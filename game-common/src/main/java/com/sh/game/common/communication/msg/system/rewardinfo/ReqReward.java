package com.sh.game.common.communication.msg.system.rewardinfo;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取奖励
 * 该文件由工具根据 rewardinfo.xml 文件自动生成，不可修改
 */
@RPC("toServer")
public class ReqReward extends AbsProtostuffMessage {
  /**
   * 领取的奖励id
   */
  private int rewardId;

  @Override
  public int getId() {
    return 397002;
  }

  public void setRewardId(int rewardId) {
    this.rewardId = rewardId;
  }

  public int getRewardId() {
    return this.rewardId;
  }
}
