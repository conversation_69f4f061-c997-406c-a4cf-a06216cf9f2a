package com.sh.game.common.communication.msg.system.roleSkillTree;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求升级技能树技能
 * 该文件由工具根据 roleSkillTree.xml 文件自动生成，不可修改
 */
@RPC("toServer")
public class ReqRoleSkillTreeLevelUpMessage extends AbsProtostuffMessage {
  /**
   * 技能树ID
   */
  private int cid;

  /**
   * 技能ID
   */
  private int skillCid;

  @Override
  public int getId() {
    return 415005;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }

  public void setSkillCid(int skillCid) {
    this.skillCid = skillCid;
  }

  public int getSkillCid() {
    return this.skillCid;
  }
}
