package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@Notice
public class MonsterDropFilterNotice extends ProcessNotice {

    private long rid;

    private int monsterCfgId;

    private int dailyType;

    private int dropType;

    private long mapKey;

    private int x;

    private int y;

    private List<int[]> dropList;

}
