package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;

/**
 * 挖矿朝向改变
 */
@Getter
@Notice
public class OreHoleTelportNotice extends ProcessNotice {

    private long rid;

    private long mapKey;

    private int face;

    private int map;

    private int x;

    private int y;

    public OreHoleTelportNotice() {
    }

    public OreHoleTelportNotice(long rid, int map, long mapKey, int x, int y, int face) {
        this.rid = rid;
        this.mapKey = mapKey;
        this.face = face;
        this.map = map;
        this.x = x;
        this.y = y;
    }
}
