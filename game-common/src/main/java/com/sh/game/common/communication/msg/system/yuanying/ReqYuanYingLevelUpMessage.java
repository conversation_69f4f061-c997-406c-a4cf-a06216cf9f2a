package com.sh.game.common.communication.msg.system.yuanying;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求元婴升级强化
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqYuanYingLevelUpMessage extends AbsProtostuffMessage {
  /**
   * 位置
   */
  private int index;

  /**
   * 强化类型（0普通强化，1付费强化）
   */
  private int type;

  /**
   * 配置id
   */
  private int cid;

  @Override
  public int getId() {
    return 311003;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  public int getIndex() {
    return this.index;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getType() {
    return this.type;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
