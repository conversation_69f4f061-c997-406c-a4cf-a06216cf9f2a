package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.communication.msg.system.recharge.bean.ThroughRechargeInfo;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 限时直购消息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResSendThroughRechargeMessage extends AbsProtostuffMessage {
  /**
   * 直购信息
   */
  private List<ThroughRechargeInfo> throughInfo = new ArrayList<>();

  @Override
  public int getId() {
    return 39011;
  }

  public void setThroughInfo(List<ThroughRechargeInfo> throughInfo) {
    this.throughInfo = throughInfo;
  }

  public List<ThroughRechargeInfo> getThroughInfo() {
    return this.throughInfo;
  }
}
