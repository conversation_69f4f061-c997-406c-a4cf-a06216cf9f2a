package com.sh.game.common.communication.msg.system.secondaryPassword;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.lang.String;

/**
 * 请求修改密码
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqUpdatePasswordMessage extends AbsProtostuffMessage {
  /**
   * 旧密码
   */
  private String oldPassword = new String();

  /**
   * 新密码
   */
  private String newPassword = new String();

  /**
   * 确认密码
   */
  private String surePassword = new String();

  @Override
  public int getId() {
    return 280007;
  }

  public void setOldPassword(String oldPassword) {
    this.oldPassword = oldPassword;
  }

  public String getOldPassword() {
    return this.oldPassword;
  }

  public void setNewPassword(String newPassword) {
    this.newPassword = newPassword;
  }

  public String getNewPassword() {
    return this.newPassword;
  }

  public void setSurePassword(String surePassword) {
    this.surePassword = surePassword;
  }

  public String getSurePassword() {
    return this.surePassword;
  }
}
