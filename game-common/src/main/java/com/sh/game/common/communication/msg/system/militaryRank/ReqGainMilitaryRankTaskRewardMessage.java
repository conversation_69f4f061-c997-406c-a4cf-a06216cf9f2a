package com.sh.game.common.communication.msg.system.militaryRank;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取军衔任务奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toServer")
public class ReqGainMilitaryRankTaskRewardMessage extends AbsProtostuffMessage {
  /**
   * 任务id
   */
  private int taskId;

  @Override
  public int getId() {
    return 366004;
  }

  public void setTaskId(int taskId) {
    this.taskId = taskId;
  }

  public int getTaskId() {
    return this.taskId;
  }
}
