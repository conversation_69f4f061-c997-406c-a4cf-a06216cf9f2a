package com.sh.game.common.communication.msg.map.bean;

import java.util.ArrayList;
import java.util.List;

public class RoundMonsterBean {
  /**
   * id
   */
  private long lid;

  /**
   * 怪物配置id
   */
  private int mid;

  /**
   * 模型id
   */
  private int model;

  /**
   * 怪物名
   */
  private String name = new String();

  /**
   * 等级
   */
  private int level;

  private int x;

  private int y;

  /**
   * 是否是传送
   */
  private boolean teleport;

  /**
   * 归属者id
   */
  private long ownerLid;

  /**
   * 归属者名字
   */
  private String ownerName = new String();

  /**
   * 朝向
   */
  private int dir;

  /**
   * 血量
   */
  private long hp;

  /**
   * 最大血量
   */
  private long maxHp;

  /**
   * buff信息
   */
  private List<PerformerBufferBean> buffers = new ArrayList<>();

  /**
   * 是否是新生怪物 1表示是新生
   */
  private int newBorn;

  /**
   * 0:无状态 1:被采集(公主)
   */
  private int state;

  /**
   * 自己已采集次数
   */
  private int collected;


  /**
   * 主人id
   */
  private long masterId;

  /**
   * 主人名字
   */
  private String masterName = new String();

  /**
   * 所在地图id
   */
  private int mapID;

  /**
   * 护盾值
   */
  private long shield;

  /**
   * 护盾值最大值
   */
  private long maxShield;

    /**
     * 全局技能
     */
    private List<Integer> globalSkills = new ArrayList<>();

  public void setLid(long lid) {
    this.lid = lid;
  }

  public long getLid() {
    return this.lid;
  }

  public void setMid(int mid) {
    this.mid = mid;
  }

  public int getMid() {
    return this.mid;
  }

  public void setModel(int model) {
    this.model = model;
  }

  public int getModel() {
    return this.model;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getName() {
    return this.name;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getLevel() {
    return this.level;
  }

  public void setX(int x) {
    this.x = x;
  }

  public int getX() {
    return this.x;
  }

  public void setY(int y) {
    this.y = y;
  }

  public int getY() {
    return this.y;
  }

  public void setTeleport(boolean teleport) {
    this.teleport = teleport;
  }

  public boolean getTeleport() {
    return this.teleport;
  }

  public void setOwnerLid(long ownerLid) {
    this.ownerLid = ownerLid;
  }

  public long getOwnerLid() {
    return this.ownerLid;
  }

  public void setOwnerName(String ownerName) {
    this.ownerName = ownerName;
  }

  public String getOwnerName() {
    return this.ownerName;
  }

  public void setDir(int dir) {
    this.dir = dir;
  }

  public int getDir() {
    return this.dir;
  }

  public void setHp(long hp) {
    this.hp = hp;
  }

  public long getHp() {
    return this.hp;
  }

  public void setMaxHp(long maxHp) {
    this.maxHp = maxHp;
  }

  public long getMaxHp() {
    return this.maxHp;
  }

  public void setBuffers(List<PerformerBufferBean> buffers) {
    this.buffers = buffers;
  }

  public List<PerformerBufferBean> getBuffers() {
    return this.buffers;
  }

  public void setNewBorn(int newBorn) {
    this.newBorn = newBorn;
  }

  public int getNewBorn() {
    return this.newBorn;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getState() {
    return this.state;
  }

  public void setCollected(int collected) {
    this.collected = collected;
  }

  public int getCollected() {
    return this.collected;
  }

  public void setMasterId(long masterId) {
    this.masterId = masterId;
  }

  public long getMasterId() {
    return this.masterId;
  }

  public void setMasterName(String masterName) {
    this.masterName = masterName;
  }

  public String getMasterName() {
    return this.masterName;
  }

  public void setMapID(int mapID) {
    this.mapID = mapID;
  }

  public int getMapID() {
    return this.mapID;
  }

  public void setShield(long shield) {
    this.shield = shield;
  }

  public long getShield() {
    return this.shield;
  }

  public void setMaxShield(long maxShield) {
    this.maxShield = maxShield;
  }

  public long getMaxShield() {
    return this.maxShield;
  }

    public List<Integer> getGlobalSkills() {
        return this.globalSkills;
  }

    public void setGlobalSkills(List<Integer> globalSkills) {
        this.globalSkills = globalSkills;
    }
}
