package com.sh.game.common.communication.msg.system.faqixiulian;

import com.sh.game.common.communication.msg.system.faqixiulian.bean.FaQiXiuLianBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回法器修炼信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResFaQiXiuLianInfoMessage extends AbsProtostuffMessage {
  /**
   * 法器修炼信息
   */
  private List<FaQiXiuLianBean> beanList = new ArrayList<>();

  @Override
  public int getId() {
    return 384003;
  }

  public void setBeanList(List<FaQiXiuLianBean> beanList) {
    this.beanList = beanList;
  }

  public List<FaQiXiuLianBean> getBeanList() {
    return this.beanList;
  }
}
