package com.sh.game.common.communication.msg.system.transformSuperMan;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求狂暴保护状态变更
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqSuperManProtectMessage extends AbsProtostuffMessage {
  /**
   * 是否开启狂暴保护，0: 关 1: 回城 2：随机
   */
  private int isOpen;

  @Override
  public int getId() {
    return 318003;
  }

  public void setIsOpen(int isOpen) {
    this.isOpen = isOpen;
  }

  public int getIsOpen() {
    return this.isOpen;
  }
}
