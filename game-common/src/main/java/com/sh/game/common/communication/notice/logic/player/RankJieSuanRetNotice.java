package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.common.entity.match.RankPlayer;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/29 16:17
 */
@Getter
@Setter
@Notice
public class RankJieSuanRetNotice extends ProcessNotice {

    private int pvpType;

    private List<RankPlayer> list = new ArrayList<>();
}
