package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-01-20
 **/
@Getter
@Setter
@Slf4j
@Notice
public class ScenceAddBuffNotice extends ProcessNotice {
    long mapUid;
    long targetUid;
    int[] buffIds;
    long casterUid;
    long hurt;
    long time;
}
