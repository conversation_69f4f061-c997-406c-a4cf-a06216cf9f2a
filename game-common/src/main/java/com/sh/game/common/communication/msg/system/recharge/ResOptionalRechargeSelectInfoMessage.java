package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.communication.msg.system.recharge.bean.OptionalRechargeSelectBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回自选充值选择信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResOptionalRechargeSelectInfoMessage extends AbsProtostuffMessage {
  /**
   * 活动id
   */
  private int actId;

  /**
   * 自选列表
   */
  private List<OptionalRechargeSelectBean> optionRechargeInfoList = new ArrayList<>();

  @Override
  public int getId() {
    return 39017;
  }

  public void setActId(int actId) {
    this.actId = actId;
  }

  public int getActId() {
    return this.actId;
  }

  public void setOptionRechargeInfoList(List<OptionalRechargeSelectBean> optionRechargeInfoList) {
    this.optionRechargeInfoList = optionRechargeInfoList;
  }

  public List<OptionalRechargeSelectBean> getOptionRechargeInfoList() {
    return this.optionRechargeInfoList;
  }
}
