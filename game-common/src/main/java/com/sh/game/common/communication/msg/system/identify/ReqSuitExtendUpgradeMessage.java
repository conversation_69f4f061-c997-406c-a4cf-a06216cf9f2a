package com.sh.game.common.communication.msg.system.identify;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求专属继承套装升级
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqSuitExtendUpgradeMessage extends AbsProtostuffMessage {
  /**
   * 待升级的专属装备uid
   */
  private long itemUid;

  @Override
  public int getId() {
    return 183006;
  }

  public void setItemUid(long itemUid) {
    this.itemUid = itemUid;
  }

  public long getItemUid() {
    return this.itemUid;
  }
}
