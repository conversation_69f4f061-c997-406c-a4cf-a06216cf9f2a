package com.sh.game.common.communication.notice.battle;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 通知地图创建成功
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/8/13.
 */
@Setter
@Getter
@Notice
public class BattleMapToMatchNotice extends ProcessNotice {
    /**
     * pvpid
     */
    private int battleId;
    /**
     * 地图id
     */
    private long mapInstId;


    private int mapCfgId;


}
