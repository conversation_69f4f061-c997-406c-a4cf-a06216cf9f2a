package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@Notice
public class TradeAppendNotice extends ProcessNotice {

    private long roleId;

    private int grid;

    private Item item;

    private boolean total = false;
}
