package com.sh.game.common.communication.msg.system.chongWuEquip;

import com.sh.game.common.communication.msg.abc.bean.RolePetQiangHuaBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回强化信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResChongWuUpSeccussMessage extends AbsProtostuffMessage {
  /**
   * 结果
   */
  private boolean success;

  /**
   * 宠物装备强化信息
   */
  private List<RolePetQiangHuaBean> pos = new ArrayList<>();

  @Override
  public int getId() {
    return 357002;
  }

  public void setSuccess(boolean success) {
    this.success = success;
  }

  public boolean getSuccess() {
    return this.success;
  }

  public void setPos(List<RolePetQiangHuaBean> pos) {
    this.pos = pos;
  }

  public List<RolePetQiangHuaBean> getPos() {
    return this.pos;
  }
}
