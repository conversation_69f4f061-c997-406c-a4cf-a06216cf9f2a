package com.sh.game.common.communication.msg.system.appearance;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 解锁
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqAppearanceUnlockMessage extends AbsProtostuffMessage {
  /**
   * 使用的解锁道具
   */
  private long itemId;

  @Override
  public int getId() {
    return 200002;
  }

  public void setItemId(long itemId) {
    this.itemId = itemId;
  }

  public long getItemId() {
    return this.itemId;
  }
}
