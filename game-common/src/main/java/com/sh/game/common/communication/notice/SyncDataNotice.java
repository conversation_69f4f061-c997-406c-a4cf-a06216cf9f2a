package com.sh.game.common.communication.notice;

import com.sh.game.common.sync.SyncBean;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Notice
@Getter
@Setter
public class SyncDataNotice extends ProcessNotice {

    private long roleId;

    private long dataId;

    private List<SyncBean> syncBeans;
}
