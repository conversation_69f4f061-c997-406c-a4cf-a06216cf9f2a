package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2025/2/13 11:34
 */
@Getter
@Setter
@Notice
public class CollectMonsterRebornPool2GameNotice extends ProcessNotice {
    private long rid;
    private long mapId;
    private int rebornItemCfgId;
    private int rebornPoolCost;
    private boolean firstUpdate;
}
