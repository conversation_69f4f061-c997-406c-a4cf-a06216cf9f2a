package com.sh.game.common.communication.msg.system.unionCamp;

import com.sh.game.common.communication.msg.system.unionCamp.bean.CampAllyBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回同阵营盟友列表信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResCampAllyInfoMessage extends AbsProtostuffMessage {
  /**
   * 行会势力信息
   */
  private List<CampAllyBean> campAllyBean = new ArrayList<>();

  @Override
  public int getId() {
    return 365006;
  }

  public void setCampAllyBean(List<CampAllyBean> campAllyBean) {
    this.campAllyBean = campAllyBean;
  }

  public List<CampAllyBean> getCampAllyBean() {
    return this.campAllyBean;
  }
}
