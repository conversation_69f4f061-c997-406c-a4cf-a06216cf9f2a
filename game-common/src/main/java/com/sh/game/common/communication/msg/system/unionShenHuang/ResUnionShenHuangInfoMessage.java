package com.sh.game.common.communication.msg.system.unionShenHuang;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回行会神皇信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResUnionShenHuangInfoMessage extends AbsProtostuffMessage {
  /**
   * 行会id
   */
  private long unionId;

  /**
   * 行会神皇id
   */
  private int unionShenHuangId;

  /**
   * 行会神皇经验
   */
  private int unionShenHuangExp;

  @Override
  public int getId() {
    return 361001;
  }

  public void setUnionId(long unionId) {
    this.unionId = unionId;
  }

  public long getUnionId() {
    return this.unionId;
  }

  public void setUnionShenHuangId(int unionShenHuangId) {
    this.unionShenHuangId = unionShenHuangId;
  }

  public int getUnionShenHuangId() {
    return this.unionShenHuangId;
  }

  public void setUnionShenHuangExp(int unionShenHuangExp) {
    this.unionShenHuangExp = unionShenHuangExp;
  }

  public int getUnionShenHuangExp() {
    return this.unionShenHuangExp;
  }
}
