package com.sh.game.common.communication.msg.system.material;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 材料副本副本奖励领取结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResGetMaterialRewardMessage extends AbsProtostuffMessage {
  /**
   * 0:成功 1:失败
   */
  private int result;

  @Override
  public int getId() {
    return 179003;
  }

  public void setResult(int result) {
    this.result = result;
  }

  public int getResult() {
    return this.result;
  }
}
