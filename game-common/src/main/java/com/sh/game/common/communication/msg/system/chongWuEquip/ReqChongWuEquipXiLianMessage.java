package com.sh.game.common.communication.msg.system.chongWuEquip;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求宠物装备洗练
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqChongWuEquipXiLianMessage extends AbsProtostuffMessage {
  /**
   * 装备id
   */
  private long equipId;

  @Override
  public int getId() {
    return 357004;
  }

  public void setEquipId(long equipId) {
    this.equipId = equipId;
  }

  public long getEquipId() {
    return this.equipId;
  }
}
