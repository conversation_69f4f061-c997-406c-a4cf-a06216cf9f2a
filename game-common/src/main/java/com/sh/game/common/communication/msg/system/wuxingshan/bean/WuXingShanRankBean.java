package com.sh.game.common.communication.msg.system.wuxingshan.bean;

import com.sh.game.common.communication.msg.system.query.bean.QueryAvatarBean;
import java.lang.String;

public class WuXingShanRankBean {
  /**
   * 玩家id
   */
  private long rid;

  /**
   * name
   */
  private String name = new String();

  /**
   * 伤害
   */
  private long hurt;

  /**
   *  排名
   */
  private int rank;

  /**
   *  玩家详情，只有前三的玩家会发
   */
  private QueryAvatarBean info = new QueryAvatarBean();

  public void setRid(long rid) {
    this.rid = rid;
  }

  public long getRid() {
    return this.rid;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getName() {
    return this.name;
  }

  public void setHurt(long hurt) {
    this.hurt = hurt;
  }

  public long getHurt() {
    return this.hurt;
  }

  public void setRank(int rank) {
    this.rank = rank;
  }

  public int getRank() {
    return this.rank;
  }

  public void setInfo(QueryAvatarBean info) {
    this.info = info;
  }

  public QueryAvatarBean getInfo() {
    return this.info;
  }
}
