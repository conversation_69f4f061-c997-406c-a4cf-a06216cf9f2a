package com.sh.game.common.communication.msg.system.handbook;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回领取图鉴进度奖励结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResHandbookRewardMessage extends AbsProtostuffMessage {
  /**
   * 图鉴进度表id
   */
  private int cid;

  @Override
  public int getId() {
    return 314013;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
