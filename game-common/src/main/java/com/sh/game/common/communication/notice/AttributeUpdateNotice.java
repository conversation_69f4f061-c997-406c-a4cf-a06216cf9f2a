package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2018/6/13 15:10
 */
@Setter
@Getter
@Notice
public class AttributeUpdateNotice extends ProcessNotice {

    private Attribute attribute;

    private long rid;

    private long actorId;

}