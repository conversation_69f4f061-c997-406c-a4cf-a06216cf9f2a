package com.sh.game.common.communication.msg.system.identify;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求资质洗练
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqAptitudeConciseMessage extends AbsProtostuffMessage {
  /**
   * 待重新鉴定的装备
   */
  private long itemUid;

  @Override
  public int getId() {
    return 183008;
  }

  public void setItemUid(long itemUid) {
    this.itemUid = itemUid;
  }

  public long getItemUid() {
    return this.itemUid;
  }
}
