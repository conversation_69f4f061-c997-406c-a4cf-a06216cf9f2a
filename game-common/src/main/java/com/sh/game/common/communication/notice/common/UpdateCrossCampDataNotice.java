package com.sh.game.common.communication.notice.common;

import com.sh.game.common.unionCamp.CrossUnionCamp;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-05-06
 **/
@Getter
@Setter
@Notice
public class UpdateCrossCampDataNotice extends ProcessNotice {
    private int campType;
    private CrossUnionCamp crossUnionCamp;
    private boolean isJoin;
}
