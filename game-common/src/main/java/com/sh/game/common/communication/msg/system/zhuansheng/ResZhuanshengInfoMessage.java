package com.sh.game.common.communication.msg.system.zhuansheng;

import com.sh.game.common.communication.msg.system.zhuansheng.bean.ZhuanShengBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回转生信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResZhuanshengInfoMessage extends AbsProtostuffMessage {
  /**
   * 转生信息
   */
  private List<ZhuanShengBean> zhuansheng = new ArrayList<>();

  @Override
  public int getId() {
    return 208002;
  }

  public void setZhuansheng(List<ZhuanShengBean> zhuansheng) {
    this.zhuansheng = zhuansheng;
  }

  public List<ZhuanShengBean> getZhuansheng() {
    return this.zhuansheng;
  }
}
