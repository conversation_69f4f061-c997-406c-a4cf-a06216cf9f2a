package com.sh.game.common.communication.msg.system.openlist;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResOpenListInfoMessage extends AbsProtostuffMessage {
  /**
   * openlist表id
   */
  private List<Integer> infos = new ArrayList<>();

  @Override
  public int getId() {
    return 395002;
  }

  public void setInfos(List<Integer> infos) {
    this.infos = infos;
  }

  public List<Integer> getInfos() {
    return this.infos;
  }
}
