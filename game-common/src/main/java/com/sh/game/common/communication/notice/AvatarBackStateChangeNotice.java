package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.avatar.AvatarBackState;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Notice
public class AvatarBackStateChangeNotice extends ProcessNotice {

    private long rid;

    private AvatarBackState avatarBackState = new AvatarBackState();

}