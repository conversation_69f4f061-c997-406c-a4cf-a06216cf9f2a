package com.sh.game.common.communication.msg.system.yuanying;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回元婴强化结果
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResYuanYingLevelUpMessage extends AbsProtostuffMessage {
  /**
   * 状态（0失败，1成功）
   */
  private int state;

  @Override
  public int getId() {
    return 311004;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getState() {
    return this.state;
  }
}
