package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Notice
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class ChongWuUpdateNotice extends ProcessNotice {

    /**
     * 玩家编号
     */
    private long roleId;

    /**
     * 宠物配置id
     */
    private int chongWuCfgId;

    /**
     * 宠物使用的怪物模型（怪物表id）
     */
    private int chongwuMonsterModel;
}
