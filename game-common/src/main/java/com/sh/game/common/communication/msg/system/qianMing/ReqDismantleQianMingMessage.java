package com.sh.game.common.communication.msg.system.qianMing;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求拆卸签名
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqDismantleQianMingMessage extends AbsProtostuffMessage {
  /**
   * 装备位
   */
  private int index;

  @Override
  public int getId() {
    return 375003;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  public int getIndex() {
    return this.index;
  }
}
