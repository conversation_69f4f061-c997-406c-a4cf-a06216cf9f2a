package com.sh.game.common.communication.msg.system.transformSuperMan;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 返回是否拥有狂暴之力
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResRoleToSuperManInfoMessage extends AbsProtostuffMessage {
  /**
   * 是否开启狂暴之力
   */
  private boolean isSuccess;

  /**
   * 是否开启狂暴保护，0: 关 1: 回城 2：随机
   */
  private int protect;

  @Override
  public int getId() {
    return 318011;
  }

  public void setIsSuccess(boolean isSuccess) {
    this.isSuccess = isSuccess;
  }

  public boolean getIsSuccess() {
    return this.isSuccess;
  }

  public void setProtect(int protect) {
    this.protect = protect;
  }

  public int getProtect() {
    return this.protect;
  }
}
