package com.sh.game.common.communication.msg.system.roleSkillTree;

import com.sh.game.common.communication.msg.system.roleSkillTree.bean.SkillTreeBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回技能数信息
 * 该文件由工具根据 roleSkillTree.xml 文件自动生成，不可修改
 */
@RPC("toClient")
public class ResRoleSkillTreeInfoMessage extends AbsProtostuffMessage {
  /**
   * 当前剩余技能点
   */
  private int skillPoint;

  /**
   * 玩家选择的技能树集合
   */
  private List<SkillTreeBean> skillBean = new ArrayList<>();

  @Override
  public int getId() {
    return 415001;
  }

  public void setSkillPoint(int skillPoint) {
    this.skillPoint = skillPoint;
  }

  public int getSkillPoint() {
    return this.skillPoint;
  }

  public void setSkillBean(List<SkillTreeBean> skillBean) {
    this.skillBean = skillBean;
  }

  public List<SkillTreeBean> getSkillBean() {
    return this.skillBean;
  }
}
