package com.sh.game.common.communication.msg.system.fightforchangan;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 领取返回
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResFightForChangAnGetRewardMessage extends AbsProtostuffMessage {
  /**
   * 我的个人积分
   */
  private int myScore;

  /**
   * 当前已领取的奖励的cfgID
   */
  private int receivedCfgID;

  @Override
  public int getId() {
    return 343005;
  }

  public void setMyScore(int myScore) {
    this.myScore = myScore;
  }

  public int getMyScore() {
    return this.myScore;
  }

  public void setReceivedCfgID(int receivedCfgID) {
    this.receivedCfgID = receivedCfgID;
  }

  public int getReceivedCfgID() {
    return this.receivedCfgID;
  }
}
