package com.sh.game.common.communication.msg.system.booksword;

import com.sh.game.common.communication.msg.system.booksword.bean.BookSwordBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回书剑信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResBookSwordInfoMessage extends AbsProtostuffMessage {
  /**
   * 玩家当前书剑信息
   */
  private List<BookSwordBean> bookSwordBeanList = new ArrayList<>();

  @Override
  public int getId() {
    return 360002;
  }

  public void setBookSwordBeanList(List<BookSwordBean> bookSwordBeanList) {
    this.bookSwordBeanList = bookSwordBeanList;
  }

  public List<BookSwordBean> getBookSwordBeanList() {
    return this.bookSwordBeanList;
  }
}
