package com.sh.game.common.communication.msg.system.functionpush;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求功能预告奖励
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqFunctionPushRewardMessage extends AbsProtostuffMessage {
  /**
   * cfg_functionpush表id
   */
  private int cid;

  @Override
  public int getId() {
    return 320003;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
