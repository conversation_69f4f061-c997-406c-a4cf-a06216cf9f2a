package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Notice
public class FightForChangAnRewardNotice extends ProcessNotice {
    //奖励配置id
    private int rewardCfgID;
    //行会
    private long unionID;
    //所在地图
    private int mapCfgID;
    //排名
    private int nRankNo;
}
