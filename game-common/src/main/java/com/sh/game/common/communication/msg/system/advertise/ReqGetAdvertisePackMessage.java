package com.sh.game.common.communication.msg.system.advertise;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求领取广告礼包
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqGetAdvertisePackMessage extends AbsProtostuffMessage {
  /**
   * 配置id
   */
  private int cid;

  @Override
  public int getId() {
    return 407002;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public int getCid() {
    return this.cid;
  }
}
