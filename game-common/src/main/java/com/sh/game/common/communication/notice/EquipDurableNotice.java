package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@Notice
public class EquipDurableNotice extends ProcessNotice {

    private long rid;

    //lid -> [pos, durable]
    private Map<Long, Integer[]> changeMap;

    //没跳档不用发
    private boolean needNotice;
}
