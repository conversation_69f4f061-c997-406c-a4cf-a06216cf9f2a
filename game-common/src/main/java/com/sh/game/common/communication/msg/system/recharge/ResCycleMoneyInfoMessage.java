package com.sh.game.common.communication.msg.system.recharge;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 周期充值金额信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResCycleMoneyInfoMessage extends AbsProtostuffMessage {
  /**
   * 当前周期
   */
  private int currentCycle;

  /**
   * 累计充值金额
   */
  private int totalMoney;

  /**
   * 已领奖金额配置编号列表
   */
  private List<Integer> receiveList = new ArrayList<>();

  @Override
  public int getId() {
    return 39020;
  }

  public void setCurrentCycle(int currentCycle) {
    this.currentCycle = currentCycle;
  }

  public int getCurrentCycle() {
    return this.currentCycle;
  }

  public void setTotalMoney(int totalMoney) {
    this.totalMoney = totalMoney;
  }

  public int getTotalMoney() {
    return this.totalMoney;
  }

  public void setReceiveList(List<Integer> receiveList) {
    this.receiveList = receiveList;
  }

  public List<Integer> getReceiveList() {
    return this.receiveList;
  }
}
