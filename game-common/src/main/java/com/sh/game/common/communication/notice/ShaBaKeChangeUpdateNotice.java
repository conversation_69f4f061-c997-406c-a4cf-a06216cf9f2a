package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

/**
 * 沙巴克切换归属之后处理
 */
@Getter
@Setter
@Notice
public class ShaBaKeChangeUpdateNotice extends ProcessNotice {
    private Set<Long> oldUnionSet = new HashSet<>();
    private Set<Long> unionSet = new HashSet<>();
}
