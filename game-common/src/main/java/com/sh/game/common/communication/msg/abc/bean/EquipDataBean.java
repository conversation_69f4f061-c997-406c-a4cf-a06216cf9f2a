package com.sh.game.common.communication.msg.abc.bean;

import java.util.ArrayList;
import java.util.List;

public class EquipDataBean {
  /**
   * 消耗耐久
   */
  private int durable;

  /**
   * 升星等级
   */
  private int starLevel;

  /**
   * 被锁星级
   */
  private int starLock;

  /**
   * 升星祝福值
   */
  private int starExp;

  /**
   * 觉醒等级
   */
  private int awakeLevel;

  /**
   * 极品属性
   */
  private List<RandAttributeBean> superAttrs = new ArrayList<>();

  /**
   * 鉴定属性
   */
  private List<IdentifyAttributeBean> identifyAttrs = new ArrayList<>();

  /**
   * 鉴定Buff
   */
  private List<IdentifyAttributeBean> identifyBuffs = new ArrayList<>();

  /**
   * 是否被封印了，1表示封印
   */
  private int fengyin;

  /**
   * 祝福等级
   */
  private int zhufu;

  /**
   * 融合的特戒id
   */
  private int tjId;

  /**
   * 祝福属性
   */
  private List<RandAttributeBean> zhufuAttrs = new ArrayList<>();

  /**
   * 魔血石剩余值
   */
  private int magicBloodStoneValue;

  /**
   * 洗练属性
   */
  private List<RandAttributeBean> xilianAttrs = new ArrayList<>();

  /**
   * 融合的魔器id
   */
  private int demonEquipId;

  /**
   * 宝石ID
   */
  private int gemId;

  /**
   * 对应的资质表id
   */
  private int aptitudeCfgId;

  /**
   * 资质装备的资质值
   */
  private int aptitudeValue;

  /**
   * 资质属性
   */
  private List<RandAttributeBean> aptitudeAttrs = new ArrayList<>();

  /**
   * 1投保，0未投保
   */
  private int toubao;

  /**
   * 装备继承的套装id
   */
  private List<Integer> suitIdList = new ArrayList<>();

  /**
   * 缔造属性
   */
  private List<RandAttributeBean> diZaoAttribute = new ArrayList<>();

  /**
   * 缔造属性万分比
   */
  private int rate;

  /**
   * cfg_destinyn_special的id
   */
  private int xingMingId;

  /**
   * 品阶
   */
  private int rare;

  /**
   * 品阶属性
   */
  private List<RandAttributeBean> rareAttrs = new ArrayList<>();

  /**
   * 法宝属性万分比
   */
  private int magicWeaponRate;

  /**
   * 镶嵌融合的法宝
   */
  private List<MagicWeaponBlendBean> magicWeaponBlendList = new ArrayList<>();

  /**
   * 法宝携带的buffId
   */
  private int magicWeaponBuffId;

  /**
   * 兽决强化等级
   */
  private int shouJueLevel;

  /**
   * 泰坦神石id
   */
  private int taiTanGemId;

  /**
   * 融合龙魂itemId列表
   */
  private List<Integer> longhunGemIdList = new ArrayList<>();

  /**
   * 淬炼等级
   */
  private int refinementLevel;

  /**
   * 缴械时间戳(ms)
   */
  private long disarmTime;

  /**
   * 淬炼额外成功率
   */
  private int refinementRate;

  /**
   * 宠物装备品质万分比
   */
  private int petEquipRate;

  /**
   * 宠物装备获得buff
   */
  private int petBuff;

  /**
   * 混沌宝石id
   */
  private int chaoticGemId;

  /**
   * 融合侠魂itemId列表
   */
  private List<Integer> xiahunGemIdList = new ArrayList<>();

  /**
   * 混沌赋灵等级
   */
  private int chaoticPowerLevel;

  /**
   * 命格等级
   */
  private int birthChartLevel;

  /**
   * 异火id
   */
  private int fireId;

  /**
   * 装备等级
   */
  private int level;


  /**
   * 装备锁定状态
   */
  private boolean isLock;

  /**
   * 属性值是否神赐
   */
  private boolean isShenCi;

  /**
   * 装备赐福
   */
  private EquipCiFuBean ciFuBean = new EquipCiFuBean();

  public void setDurable(int durable) {
    this.durable = durable;
  }

  public int getDurable() {
    return this.durable;
  }

  public void setStarLevel(int starLevel) {
    this.starLevel = starLevel;
  }

  public int getStarLevel() {
    return this.starLevel;
  }

  public void setStarLock(int starLock) {
    this.starLock = starLock;
  }

  public int getStarLock() {
    return this.starLock;
  }

  public void setStarExp(int starExp) {
    this.starExp = starExp;
  }

  public int getStarExp() {
    return this.starExp;
  }

  public void setAwakeLevel(int awakeLevel) {
    this.awakeLevel = awakeLevel;
  }

  public int getAwakeLevel() {
    return this.awakeLevel;
  }

  public void setSuperAttrs(List<RandAttributeBean> superAttrs) {
    this.superAttrs = superAttrs;
  }

  public List<RandAttributeBean> getSuperAttrs() {
    return this.superAttrs;
  }

  public void setIdentifyAttrs(List<IdentifyAttributeBean> identifyAttrs) {
    this.identifyAttrs = identifyAttrs;
  }

  public List<IdentifyAttributeBean> getIdentifyAttrs() {
    return this.identifyAttrs;
  }

  public void setIdentifyBuffs(List<IdentifyAttributeBean> identifyBuffs) {
    this.identifyBuffs = identifyBuffs;
  }

  public List<IdentifyAttributeBean> getIdentifyBuffs() {
    return this.identifyBuffs;
  }

  public void setFengyin(int fengyin) {
    this.fengyin = fengyin;
  }

  public int getFengyin() {
    return this.fengyin;
  }

  public void setZhufu(int zhufu) {
    this.zhufu = zhufu;
  }

  public int getZhufu() {
    return this.zhufu;
  }

  public void setTjId(int tjId) {
    this.tjId = tjId;
  }

  public int getTjId() {
    return this.tjId;
  }

  public void setZhufuAttrs(List<RandAttributeBean> zhufuAttrs) {
    this.zhufuAttrs = zhufuAttrs;
  }

  public List<RandAttributeBean> getZhufuAttrs() {
    return this.zhufuAttrs;
  }

  public void setMagicBloodStoneValue(int magicBloodStoneValue) {
    this.magicBloodStoneValue = magicBloodStoneValue;
  }

  public int getMagicBloodStoneValue() {
    return this.magicBloodStoneValue;
  }

  public void setXilianAttrs(List<RandAttributeBean> xilianAttrs) {
    this.xilianAttrs = xilianAttrs;
  }

  public List<RandAttributeBean> getXilianAttrs() {
    return this.xilianAttrs;
  }

  public void setDemonEquipId(int demonEquipId) {
    this.demonEquipId = demonEquipId;
  }

  public int getDemonEquipId() {
    return this.demonEquipId;
  }

  public void setGemId(int gemId) {
    this.gemId = gemId;
  }

  public int getGemId() {
    return this.gemId;
  }

  public void setAptitudeCfgId(int aptitudeCfgId) {
    this.aptitudeCfgId = aptitudeCfgId;
  }

  public int getAptitudeCfgId() {
    return this.aptitudeCfgId;
  }

  public void setAptitudeValue(int aptitudeValue) {
    this.aptitudeValue = aptitudeValue;
  }

  public int getAptitudeValue() {
    return this.aptitudeValue;
  }

  public void setAptitudeAttrs(List<RandAttributeBean> aptitudeAttrs) {
    this.aptitudeAttrs = aptitudeAttrs;
  }

  public List<RandAttributeBean> getAptitudeAttrs() {
    return this.aptitudeAttrs;
  }

  public void setToubao(int toubao) {
    this.toubao = toubao;
  }

  public int getToubao() {
    return this.toubao;
  }

  public void setSuitIdList(List<Integer> suitIdList) {
    this.suitIdList = suitIdList;
  }

  public List<Integer> getSuitIdList() {
    return this.suitIdList;
  }

  public void setDiZaoAttribute(List<RandAttributeBean> diZaoAttribute) {
    this.diZaoAttribute = diZaoAttribute;
  }

  public List<RandAttributeBean> getDiZaoAttribute() {
    return this.diZaoAttribute;
  }

  public void setRate(int rate) {
    this.rate = rate;
  }

  public int getRate() {
    return this.rate;
  }

  public void setXingMingId(int xingMingId) {
    this.xingMingId = xingMingId;
  }

  public int getXingMingId() {
    return this.xingMingId;
  }

  public void setRare(int rare) {
    this.rare = rare;
  }

  public int getRare() {
    return this.rare;
  }

  public void setRareAttrs(List<RandAttributeBean> rareAttrs) {
    this.rareAttrs = rareAttrs;
  }

  public List<RandAttributeBean> getRareAttrs() {
    return this.rareAttrs;
  }

  public void setMagicWeaponRate(int magicWeaponRate) {
    this.magicWeaponRate = magicWeaponRate;
  }

  public int getMagicWeaponRate() {
    return this.magicWeaponRate;
  }

  public void setMagicWeaponBlendList(List<MagicWeaponBlendBean> magicWeaponBlendList) {
    this.magicWeaponBlendList = magicWeaponBlendList;
  }

  public List<MagicWeaponBlendBean> getMagicWeaponBlendList() {
    return this.magicWeaponBlendList;
  }

  public void setMagicWeaponBuffId(int magicWeaponBuffId) {
    this.magicWeaponBuffId = magicWeaponBuffId;
  }

  public int getMagicWeaponBuffId() {
    return this.magicWeaponBuffId;
  }

  public void setShouJueLevel(int shouJueLevel) {
    this.shouJueLevel = shouJueLevel;
  }

  public int getShouJueLevel() {
    return this.shouJueLevel;
  }

  public void setTaiTanGemId(int taiTanGemId) {
    this.taiTanGemId = taiTanGemId;
  }

  public int getTaiTanGemId() {
    return this.taiTanGemId;
  }

  public void setLonghunGemIdList(List<Integer> longhunGemIdList) {
    this.longhunGemIdList = longhunGemIdList;
  }

  public List<Integer> getLonghunGemIdList() {
    return this.longhunGemIdList;
  }

  public void setRefinementLevel(int refinementLevel) {
    this.refinementLevel = refinementLevel;
  }

  public int getRefinementLevel() {
    return this.refinementLevel;
  }

  public void setDisarmTime(long disarmTime) {
    this.disarmTime = disarmTime;
  }

  public long getDisarmTime() {
    return this.disarmTime;
  }

  public void setRefinementRate(int refinementRate) {
    this.refinementRate = refinementRate;
  }

  public int getRefinementRate() {
    return this.refinementRate;
  }

  public void setPetEquipRate(int petEquipRate) {
    this.petEquipRate = petEquipRate;
  }

  public int getPetEquipRate() {
    return this.petEquipRate;
  }

  public void setPetBuff(int petBuff) {
    this.petBuff = petBuff;
  }

  public int getPetBuff() {
    return this.petBuff;
  }

  public void setChaoticGemId(int chaoticGemId) {
    this.chaoticGemId = chaoticGemId;
  }

  public int getChaoticGemId() {
    return this.chaoticGemId;
  }

  public void setXiahunGemIdList(List<Integer> xiahunGemIdList) {
    this.xiahunGemIdList = xiahunGemIdList;
  }

  public List<Integer> getXiahunGemIdList() {
    return this.xiahunGemIdList;
  }

  public void setChaoticPowerLevel(int chaoticPowerLevel) {
    this.chaoticPowerLevel = chaoticPowerLevel;
  }

  public int getChaoticPowerLevel() {
    return this.chaoticPowerLevel;
  }

  public void setBirthChartLevel(int birthChartLevel) {
    this.birthChartLevel = birthChartLevel;
  }

  public int getBirthChartLevel() {
    return this.birthChartLevel;
  }

  public void setFireId(int fireId) {
    this.fireId = fireId;
  }

  public int getFireId() {
    return this.fireId;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getLevel() {
    return this.level;
  }

  public void setIsLock(boolean isLock) {
    this.isLock = isLock;
  }

  public boolean getIsLock() {
    return this.isLock;
  }

  public void setIsShenCi(boolean isShenCi) {
    this.isShenCi = isShenCi;
  }

  public boolean getIsShenCi() {
    return this.isShenCi;
  }

  public void setCiFuBean(EquipCiFuBean ciFuBean) {
    this.ciFuBean = ciFuBean;
  }

  public EquipCiFuBean getCiFuBean() {
    return this.ciFuBean;
  }
}
