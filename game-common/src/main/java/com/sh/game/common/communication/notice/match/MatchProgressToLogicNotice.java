package com.sh.game.common.communication.notice.match;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 匹配进度
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/8/13
 * .
 */
@Setter
@Getter
@Notice
public class MatchProgressToLogicNotice extends ProcessNotice {

    /**
     * 组id
     */
    private List<Long> groupId = new ArrayList<>();
    /**
     * 进度
     */
    private int process;
    /**
     * 信息
     */
    private String info;

}
