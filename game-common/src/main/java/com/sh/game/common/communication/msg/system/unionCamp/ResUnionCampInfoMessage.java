package com.sh.game.common.communication.msg.system.unionCamp;

import com.sh.game.common.communication.msg.system.unionCamp.bean.CampPowerBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回阵营信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResUnionCampInfoMessage extends AbsProtostuffMessage {
  /**
   * 玩家所属阵营类型
   */
  private int campType;

  /**
   * 阵营势力值
   */
  private List<CampPowerBean> campPowerBean = new ArrayList<>();

  /**
   * 玩家所属行会可加入阵营时间戳(秒), 0为无限制
   */
  private int canJoinStamp;

  @Override
  public int getId() {
    return 365002;
  }

  public void setCampType(int campType) {
    this.campType = campType;
  }

  public int getCampType() {
    return this.campType;
  }

  public void setCampPowerBean(List<CampPowerBean> campPowerBean) {
    this.campPowerBean = campPowerBean;
  }

  public List<CampPowerBean> getCampPowerBean() {
    return this.campPowerBean;
  }

  public void setCanJoinStamp(int canJoinStamp) {
    this.canJoinStamp = canJoinStamp;
  }

  public int getCanJoinStamp() {
    return this.canJoinStamp;
  }
}
