package com.sh.game.common.communication.msg.system.birthchart;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Long;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 请求命格合成
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqBirthChartCompoundMessage extends AbsProtostuffMessage {
  /**
   * 命格合成表配置id
   */
  private int configId;

  /**
   * 消耗的唯一id列表(需要按照顺序)
   */
  private List<Long> costIdList = new ArrayList<>();

  @Override
  public int getId() {
    return 380009;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setCostIdList(List<Long> costIdList) {
    this.costIdList = costIdList;
  }

  public List<Long> getCostIdList() {
    return this.costIdList;
  }
}
