package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Notice
public class AnnouncePostNotice extends ProcessNotice {

    private long param;

    private List<Integer> channel = new ArrayList<>();

    private int announceId;

    private List<Object> params = new ArrayList<>();
}
