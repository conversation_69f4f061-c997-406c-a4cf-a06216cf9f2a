package com.sh.game.common.communication.msg.system.mindGhost;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 心魔出现
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResMindGhostAppearMessage extends AbsProtostuffMessage {
  /**
   * 地图
   */
  private int map;

  private int x;

  private int y;

  /**
   * 过期时间 时间戳
   */
  private int expire;

  /**
   * 怪物配置id
   */
  private int monsterId;

  @Override
  public int getId() {
    return 201001;
  }

  public void setMap(int map) {
    this.map = map;
  }

  public int getMap() {
    return this.map;
  }

  public void setX(int x) {
    this.x = x;
  }

  public int getX() {
    return this.x;
  }

  public void setY(int y) {
    this.y = y;
  }

  public int getY() {
    return this.y;
  }

  public void setExpire(int expire) {
    this.expire = expire;
  }

  public int getExpire() {
    return this.expire;
  }

  public void setMonsterId(int monsterId) {
    this.monsterId = monsterId;
  }

  public int getMonsterId() {
    return this.monsterId;
  }
}
