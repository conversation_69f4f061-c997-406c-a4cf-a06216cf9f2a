package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.buff.Buff;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * Buff持久化通知
 *
 * <AUTHOR>
 * @since 2022-04-15 11:30
 **/
@Getter
@Setter
@Notice
public class BuffSaveNotice extends ProcessNotice {

    /**
     * 角色id
     */
    private long roleId;

    /**
     * buff
     */
    private Map<Integer, Buff> buffMap;
}
