package com.sh.game.common.communication.msg.system.fightforchangan;

import com.sh.game.common.communication.msg.system.fightforchangan.bean.PlayScoreRankingBean;
import com.sh.game.common.communication.msg.system.fightforchangan.bean.UnionScoreRankingBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 战场战斗排行信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResFightForChangAnRankingMessage extends AbsProtostuffMessage {
  /**
   * 玩家积分排行
   */
  private List<PlayScoreRankingBean> playRankingList = new ArrayList<>();

  /**
   * 自己的积分及排名
   */
  private PlayScoreRankingBean myScore = new PlayScoreRankingBean();

  /**
   * 行会积分排行
   */
  private List<UnionScoreRankingBean> unionRankingList = new ArrayList<>();

  /**
   * 自己行会积分情况
   */
  private UnionScoreRankingBean myUnion = new UnionScoreRankingBean();

  @Override
  public int getId() {
    return 343002;
  }

  public void setPlayRankingList(List<PlayScoreRankingBean> playRankingList) {
    this.playRankingList = playRankingList;
  }

  public List<PlayScoreRankingBean> getPlayRankingList() {
    return this.playRankingList;
  }

  public void setMyScore(PlayScoreRankingBean myScore) {
    this.myScore = myScore;
  }

  public PlayScoreRankingBean getMyScore() {
    return this.myScore;
  }

  public void setUnionRankingList(List<UnionScoreRankingBean> unionRankingList) {
    this.unionRankingList = unionRankingList;
  }

  public List<UnionScoreRankingBean> getUnionRankingList() {
    return this.unionRankingList;
  }

  public void setMyUnion(UnionScoreRankingBean myUnion) {
    this.myUnion = myUnion;
  }

  public UnionScoreRankingBean getMyUnion() {
    return this.myUnion;
  }
}
