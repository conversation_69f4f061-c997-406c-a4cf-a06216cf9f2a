package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * ATO：yumo<br>;
 * 时间：2021/3/22 14:56<br>;
 * 版本：1.0<br>;
 * 描述：队长变更通知
 */
@Notice
@Getter
@Setter
public class TeamLeaderNotice extends ProcessNotice {

    /**
     * 队伍编号
     */
    private long teamID;

    /**
     * 队长昵称
     */
    private long leader;

}
