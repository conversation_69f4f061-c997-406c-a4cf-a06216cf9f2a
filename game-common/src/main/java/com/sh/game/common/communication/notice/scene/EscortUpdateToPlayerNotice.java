package com.sh.game.common.communication.notice.scene;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Notice
public class EscortUpdateToPlayerNotice extends ProcessNotice {

    private long roleId;

    private int escortCid;

    private long escorting;

    private int expire;

    private long hp;

    private long mapId;

    private int x;

    private int y;

    private int status;

    /**
     * 摧毁镖车的玩家 ID
     */
    private long killerId;

}
