package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

/**
 * ATO：yumo<br>;
 * 时间：2020/12/20 10:57<br>;
 * 版本：1.0<br>;
 * 描述：队伍解散
 */
@Getter
@Setter
@Notice
public class TeamDismissNotice extends ProcessNotice {

    /**
     * 队伍成员
     */
    private Set<Long> teamMembers;

    /**
     * 队伍编号
     */
    private long teamID;

    /**
     * 队伍类型
     */
    private int teamType;
}
